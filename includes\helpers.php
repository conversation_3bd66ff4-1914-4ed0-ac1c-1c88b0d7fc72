<?php
/**
 * Helper functions for Redco Diagnostic & Auto-Fix
 *
 * Standalone utility functions extracted from main Redco Optimizer plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get plugin options
 */
function redco_diagnostic_get_option($option_name, $default = null) {
    if ($option_name === null || $option_name === '') {
        return $default;
    }

    $options = get_option('redco_diagnostic_options', array());
    return isset($options[$option_name]) ? $options[$option_name] : $default;
}

/**
 * Update plugin option
 */
function redco_diagnostic_update_option($option_name, $value) {
    if ($option_name === null || $option_name === '') {
        return false;
    }

    $options = get_option('redco_diagnostic_options', array());
    $options[$option_name] = $value;
    return update_option('redco_diagnostic_options', $options);
}

/**
 * Check if feature is enabled
 */
function redco_diagnostic_is_feature_enabled($feature) {
    if ($feature === null || $feature === '') {
        return false;
    }

    $enabled_features = redco_diagnostic_get_option('enabled_features', array(
        'tiered_fixes' => true,
        'preview_system' => true,
        'scheduling' => true,
        'enhanced_backup' => true,
        'realtime_monitoring' => true,
        'core_web_vitals' => true,
        'advanced_analytics' => true
    ));
    
    return isset($enabled_features[$feature]) ? $enabled_features[$feature] : false;
}

/**
 * Get file size in human readable format
 */
function redco_diagnostic_format_bytes($size, $precision = 2) {
    if ($size <= 0) {
        return '0 B';
    }
    
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }

    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * UNIFIED: Get centralized backup directory for all Redco operations
 */
function redco_get_unified_backup_dir() {
    $upload_dir = wp_upload_dir();
    $backup_dir = $upload_dir['basedir'] . '/redco-backups/';

    // Create directory if it doesn't exist
    if (!file_exists($backup_dir)) {
        wp_mkdir_p($backup_dir);

        // Add .htaccess for security
        $htaccess_content = "# Redco Optimizer Unified Backup Directory\n";
        $htaccess_content .= "Order deny,allow\n";
        $htaccess_content .= "Deny from all\n";
        $htaccess_content .= "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "deny from all\n";
        $htaccess_content .= "</Files>\n";

        file_put_contents($backup_dir . '.htaccess', $htaccess_content);
        error_log("REDCO UNIFIED: Created unified backup directory: {$backup_dir}");
    }

    return $backup_dir;
}

/**
 * LEGACY COMPATIBILITY: Get cache directory path (now uses unified backup for backups)
 */
function redco_diagnostic_get_cache_dir($type = 'cache') {
    $upload_dir = wp_upload_dir();

    // UNIFIED: Use centralized backup directory for all backup operations
    if ($type === 'backup') {
        return redco_get_unified_backup_dir();
    }

    // Keep other cache types in their existing locations
    $cache_base = $upload_dir['basedir'] . '/redco-diagnostic/';

    switch ($type) {
        case 'reports':
            $cache_dir = $cache_base . 'reports/';
            break;
        case 'monitoring':
            $cache_dir = $cache_base . 'monitoring/';
            break;
        default:
            $cache_dir = $cache_base . 'cache/';
            break;
    }

    // Create directory if it doesn't exist
    if (!file_exists($cache_dir)) {
        wp_mkdir_p($cache_dir);

        // Add .htaccess for security
        $htaccess_content = "# Redco Diagnostic Cache Directory\n";
        $htaccess_content .= "Options -Indexes\n";
        $htaccess_content .= "<Files *.php>\n";
        $htaccess_content .= "deny from all\n";
        $htaccess_content .= "</Files>\n";

        file_put_contents($cache_dir . '.htaccess', $htaccess_content);
    }

    return $cache_dir;
}

/**
 * Log diagnostic events
 */
function redco_diagnostic_log($message, $level = 'info') {
    if (!redco_diagnostic_get_option('enable_logging', true)) {
        return;
    }
    
    $log_file = redco_diagnostic_get_cache_dir('logs') . 'diagnostic.log';
    $timestamp = current_time('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}\n";
    
    error_log($log_entry, 3, $log_file);
}

/**
 * Get current memory usage
 */
function redco_diagnostic_get_memory_usage() {
    return memory_get_usage(true);
}

/**
 * Get peak memory usage
 */
function redco_diagnostic_get_peak_memory_usage() {
    return memory_get_peak_usage(true);
}

/**
 * Check if WooCommerce is active
 */
function redco_diagnostic_is_woocommerce_active() {
    return class_exists('WooCommerce');
}

/**
 * Check if this is a development environment
 */
function redco_diagnostic_is_development_environment() {
    // Check for common development indicators
    $dev_indicators = array(
        'localhost',
        '127.0.0.1',
        '.local',
        '.dev',
        '.test',
        'staging',
        'dev.'
    );
    
    $site_url = get_site_url();
    
    foreach ($dev_indicators as $indicator) {
        if (strpos($site_url, $indicator) !== false) {
            return true;
        }
    }
    
    // Check for WP_DEBUG
    if (defined('WP_DEBUG') && WP_DEBUG) {
        return true;
    }
    
    return false;
}

/**
 * Get all post types
 */
function redco_diagnostic_get_post_types() {
    $post_types = get_post_types(array('public' => true), 'objects');
    $types = array();

    foreach ($post_types as $post_type) {
        $types[$post_type->name] = $post_type->label;
    }

    return $types;
}

/**
 * Generate unique scan ID
 */
function redco_diagnostic_generate_scan_id() {
    return md5(uniqid(rand(), true));
}

/**
 * Get database size
 */
function redco_diagnostic_get_database_size() {
    global $wpdb;
    
    $result = $wpdb->get_var(
        "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size' 
         FROM information_schema.tables 
         WHERE table_schema = '{$wpdb->dbname}'"
    );
    
    return $result ? $result : 0;
}

/**
 * Get autoload data size
 */
function redco_diagnostic_get_autoload_size() {
    global $wpdb;

    $result = $wpdb->get_var("
        SELECT SUM(LENGTH(option_value))
        FROM {$wpdb->options}
        WHERE autoload = 'yes'
    ");

    return $result ? (int) $result : 0;
}

/**
 * Check if SSL is enabled
 */
function redco_diagnostic_is_ssl_enabled() {
    return is_ssl() || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
}

/**
 * Get WordPress version
 */
function redco_diagnostic_get_wp_version() {
    global $wp_version;
    return $wp_version;
}

/**
 * Get PHP version
 */
function redco_diagnostic_get_php_version() {
    return PHP_VERSION;
}

/**
 * Get MySQL version
 */
function redco_diagnostic_get_mysql_version() {
    global $wpdb;
    return $wpdb->db_version();
}

/**
 * Check if object cache is enabled
 */
function redco_diagnostic_is_object_cache_enabled() {
    return wp_using_ext_object_cache();
}

/**
 * Get server software
 */
function redco_diagnostic_get_server_software() {
    return isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : 'Unknown';
}

/**
 * Format time duration
 */
function redco_diagnostic_format_duration($seconds) {
    if ($seconds < 60) {
        return round($seconds, 2) . ' seconds';
    } elseif ($seconds < 3600) {
        return round($seconds / 60, 1) . ' minutes';
    } else {
        return round($seconds / 3600, 1) . ' hours';
    }
}

/**
 * Get plugin statistics
 */
function redco_diagnostic_get_plugin_stats() {
    $stats = get_option('redco_diagnostic_stats', array(
        'total_scans' => 0,
        'total_fixes_applied' => 0,
        'total_issues_found' => 0,
        'last_scan_time' => 0,
        'average_scan_time' => 0,
        'performance_improvements' => 0
    ));
    
    return $stats;
}

/**
 * Update plugin statistics
 */
function redco_diagnostic_update_stats($key, $value) {
    $stats = redco_diagnostic_get_plugin_stats();
    $stats[$key] = $value;
    update_option('redco_diagnostic_stats', $stats);
}

/**
 * Increment plugin statistic
 */
function redco_diagnostic_increment_stat($key, $amount = 1) {
    $stats = redco_diagnostic_get_plugin_stats();
    $stats[$key] = isset($stats[$key]) ? $stats[$key] + $amount : $amount;
    update_option('redco_diagnostic_stats', $stats);
}

/**
 * Check if multisite
 */
function redco_diagnostic_is_multisite() {
    return is_multisite();
}

/**
 * Get current site ID (for multisite)
 */
function redco_diagnostic_get_current_site_id() {
    return get_current_blog_id();
}

/**
 * Get all sites (for multisite)
 */
function redco_diagnostic_get_all_sites() {
    if (!is_multisite()) {
        return array();
    }
    
    return get_sites(array(
        'number' => 0,
        'orderby' => 'domain'
    ));
}

/**
 * Check user capabilities
 */
function redco_diagnostic_current_user_can($capability) {
    return current_user_can($capability);
}

/**
 * Sanitize input data
 */
function redco_diagnostic_sanitize_input($input, $type = 'text') {
    switch ($type) {
        case 'email':
            return sanitize_email($input);
        case 'url':
            return esc_url_raw($input);
        case 'int':
            return intval($input);
        case 'float':
            return floatval($input);
        case 'bool':
            return (bool) $input;
        case 'array':
            return is_array($input) ? array_map('sanitize_text_field', $input) : array();
        default:
            return sanitize_text_field($input);
    }
}

/**
 * Validate nonce
 */
function redco_diagnostic_verify_nonce($nonce, $action = 'redco_diagnostic_nonce') {
    return wp_verify_nonce($nonce, $action);
}

/**
 * Create nonce
 */
function redco_diagnostic_create_nonce($action = 'redco_diagnostic_nonce') {
    return wp_create_nonce($action);
}
