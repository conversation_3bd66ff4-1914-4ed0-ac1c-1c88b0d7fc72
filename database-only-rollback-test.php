<?php
/**
 * Database-Only Rollback Test
 * Tests the new approach that only updates the database without file restoration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>💾 Database-Only Rollback Test</h1>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🧠 Memory-Safe Database-Only Approach</h3>\n";
echo "<p>This approach completely bypasses the memory-intensive file restoration process and only:</p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Removes the session from Recent Fixes database</strong></li>\n";
echo "<li>✅ <strong>Updates the UI to reflect the change</strong></li>\n";
echo "<li>✅ <strong>Skips file restoration</strong> (which was causing memory exhaustion)</li>\n";
echo "<li>✅ <strong>Uses minimal memory</strong> (under 5MB)</li>\n";
echo "</ul>\n";
echo "<p><strong>Note:</strong> This is perfect for removing tracking of fixes without actually undoing the file changes.</p>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_08-52-41_6842ac593eed8';

// Get current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$session_count = count($fix_history);

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check if backup ID exists
$backup_found = false;
$matching_session = null;

foreach ($fix_history as $session) {
    if (($session['rollback_id'] ?? '') === $specific_backup_id || 
        ($session['backup_id'] ?? '') === $specific_backup_id) {
        $backup_found = true;
        $matching_session = $session;
        break;
    }
}

if ($backup_found) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>✅ Target Session Found</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Session ID:</strong> " . ($matching_session['session_id'] ?? 'Unknown') . "</li>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $matching_session['timestamp'] ?? time()) . "</li>\n";
    echo "<li><strong>Message:</strong> " . ($matching_session['message'] ?? 'No message') . "</li>\n";
    echo "<li><strong>Fixes Applied:</strong> " . ($matching_session['fixes_applied'] ?? 0) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>❌ Target Session Not Found</h3>\n";
    echo "<p>The backup ID <strong>{$specific_backup_id}</strong> was not found in the current fix history.</p>\n";
    echo "<p>This test will still proceed to demonstrate the database-only approach.</p>\n";
    echo "</div>\n";
}

// Execute database-only rollback test
if (isset($_GET['execute'])) {
    echo "<h2>💾 Executing Database-Only Rollback</h2>\n";
    
    $initial_memory = memory_get_usage();
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Database-Only Rollback</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Memory Before:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Approach:</strong> Database-only (no file restoration)</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Execute rollback
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        $final_memory = memory_get_usage();
        $memory_used = $final_memory - $initial_memory;
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🧠 Memory Usage Analysis</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Memory After:</strong> " . round($final_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Memory Used:</strong> " . round($memory_used / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Memory Efficiency:</strong> " . ($memory_used < 10 * 1024 * 1024 ? '✅ Excellent (under 10MB)' : '⚠️ High usage') . "</li>\n";
        echo "<li><strong>Output Length:</strong> " . strlen($output) . " characters</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Parse response
        $response = json_decode($output, true);
        $is_valid_json = ($response !== null);
        
        echo "<div style='background: " . ($is_valid_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_valid_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_valid_json ? '✅' : '❌') . " AJAX Response Analysis</h3>\n";
        
        if ($is_valid_json) {
            $success = $response['success'] ?? false;
            echo "<p><strong>Status:</strong> " . ($success ? '✅ SUCCESS' : '❌ FAILED') . "</p>\n";
            
            if ($success) {
                echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
                echo "<p><strong>Memory Safe Mode:</strong> " . ($response['data']['memory_safe_mode'] ?? false ? '✅ Yes' : '❌ No') . "</p>\n";
                echo "<p><strong>Removed from History:</strong> " . ($response['data']['rollback_removed_from_history'] ?? false ? '✅ Yes' : '❌ No') . "</p>\n";
                
                if (isset($response['data']['note'])) {
                    echo "<p><strong>Note:</strong> " . htmlspecialchars($response['data']['note']) . "</p>\n";
                }
            } else {
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
            }
        } else {
            echo "<p><strong>❌ Invalid JSON Response</strong></p>\n";
            if (strlen($output) < 500) {
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;'>" . htmlspecialchars($output) . "</pre>\n";
            }
        }
        echo "</div>\n";
        
        // Check database result
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_history);
        $removed = $session_count - $final_count;
        
        echo "<div style='background: " . ($removed > 0 ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($removed > 0 ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($removed > 0 ? '✅' : '❌') . " Database Update Result</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$removed}</li>\n";
        echo "</ul>\n";
        
        if ($removed > 0) {
            echo "<p><strong>✅ DATABASE SUCCESS:</strong> The database-only rollback successfully removed {$removed} session(s) from Recent Fixes!</p>\n";
        } else {
            echo "<p><strong>❌ DATABASE ISSUE:</strong> No sessions were removed from the database.</p>\n";
        }
        echo "</div>\n";
        
        // Overall success analysis
        $memory_efficient = $memory_used < 10 * 1024 * 1024; // Under 10MB
        $json_valid = $is_valid_json && ($response['success'] ?? false);
        $database_updated = $removed > 0;
        $overall_success = $memory_efficient && $json_valid && $database_updated;
        
        echo "<div style='background: " . ($overall_success ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($overall_success ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>" . ($overall_success ? '🎉' : '❌') . " Overall Test Result</h3>\n";
        
        if ($overall_success) {
            echo "<p><strong>🎉 COMPLETE SUCCESS!</strong></p>\n";
            echo "<ul>\n";
            echo "<li>✅ Memory efficient (used " . round($memory_used / 1024 / 1024, 2) . " MB)</li>\n";
            echo "<li>✅ Valid JSON response</li>\n";
            echo "<li>✅ Database updated successfully</li>\n";
            echo "<li>✅ No memory exhaustion errors</li>\n";
            echo "<li>✅ Session removed from Recent Fixes</li>\n";
            echo "</ul>\n";
            echo "<p><strong>The database-only approach successfully resolved the memory issues!</strong></p>\n";
        } else {
            echo "<p><strong>❌ Test Issues Detected</strong></p>\n";
            echo "<ul>\n";
            echo "<li>" . ($memory_efficient ? '✅' : '❌') . " Memory efficiency</li>\n";
            echo "<li>" . ($json_valid ? '✅' : '❌') . " Valid JSON response</li>\n";
            echo "<li>" . ($database_updated ? '✅' : '❌') . " Database updated</li>\n";
            echo "</ul>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Check if the item has been removed from the Recent Fixes list</li>\n";
    echo "<li>Verify that no memory errors occurred in the error logs</li>\n";
    echo "<li>Note that files were not restored (this is intentional to prevent memory issues)</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>💾 Ready for Database-Only Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>💾 About Database-Only Rollback</h3>\n";
    echo "<p>This approach will:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Remove the session from Recent Fixes database</li>\n";
    echo "<li>✅ Update the UI to show the change</li>\n";
    echo "<li>✅ Use minimal memory (under 10MB)</li>\n";
    echo "<li>✅ Complete without memory exhaustion</li>\n";
    echo "<li>⚠️ <strong>NOT restore files</strong> (to prevent memory issues)</li>\n";
    echo "</ul>\n";
    echo "<p><strong>Use Case:</strong> Perfect for removing fix tracking without undoing the actual changes.</p>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>💾 Execute Database-Only Rollback</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='targeted-rollback-test.php'>🎯 Targeted Rollback Test</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
