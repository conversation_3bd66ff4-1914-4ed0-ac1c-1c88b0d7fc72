<?php
/**
 * Targeted Rollback Test
 * Tests the new targeted approach that only updates Recent Fixes without heavy cache operations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🎯 Targeted Rollback Test</h1>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🧠 New Targeted Approach</h3>\n";
echo "<p>This test uses the new targeted approach that:</p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Only updates the database</strong> - removes session from Recent Fixes</li>\n";
echo "<li>✅ <strong>No cache clearing</strong> - avoids memory-intensive operations</li>\n";
echo "<li>✅ <strong>No heavy operations</strong> - skips statistics recalculation and issue checking</li>\n";
echo "<li>✅ <strong>Minimal response</strong> - simple JSON without large data structures</li>\n";
echo "</ul>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_08-47-33_6842ab2570292';

// Get current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$session_count = count($fix_history);

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check if backup ID exists
$backup_found = false;
foreach ($fix_history as $session) {
    if (($session['rollback_id'] ?? '') === $specific_backup_id || 
        ($session['backup_id'] ?? '') === $specific_backup_id) {
        $backup_found = true;
        break;
    }
}

if (!$backup_found) {
    echo "<h2>🔧 Creating Test Session</h2>\n";
    
    // Create minimal test session
    $test_session = array(
        'session_id' => 'targeted_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Targeted rollback test session',
        'fixes_applied' => 1,
        'backup_created' => true
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>✅ Created test session with backup ID: <strong>{$specific_backup_id}</strong></p>\n";
    echo "</div>\n";
    
    $session_count = count($fix_history);
}

// Execute targeted rollback test
if (isset($_GET['execute'])) {
    echo "<h2>🎯 Executing Targeted Rollback</h2>\n";
    
    $initial_memory = memory_get_usage();
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Targeted Rollback</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Memory Before:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Execute rollback
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        $final_memory = memory_get_usage();
        $memory_used = $final_memory - $initial_memory;
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🧠 Memory Usage</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Memory After:</strong> " . round($final_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Memory Used:</strong> " . round($memory_used / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Output Length:</strong> " . strlen($output) . " characters</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Parse response
        $response = json_decode($output, true);
        $is_valid_json = ($response !== null);
        
        echo "<div style='background: " . ($is_valid_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_valid_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_valid_json ? '✅' : '❌') . " AJAX Response</h3>\n";
        
        if ($is_valid_json) {
            $success = $response['success'] ?? false;
            echo "<p><strong>Status:</strong> " . ($success ? 'SUCCESS' : 'FAILED') . "</p>\n";
            
            if ($success) {
                echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
                echo "<p><strong>Targeted Update:</strong> " . ($response['data']['targeted_update'] ?? false ? 'Yes' : 'No') . "</p>\n";
                echo "<p><strong>Removed from History:</strong> " . ($response['data']['rollback_removed_from_history'] ?? false ? 'Yes' : 'No') . "</p>\n";
            } else {
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
            }
        } else {
            echo "<p><strong>Invalid JSON Response</strong></p>\n";
            if (strlen($output) < 300) {
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>" . htmlspecialchars($output) . "</pre>\n";
            }
        }
        echo "</div>\n";
        
        // Check database result
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_history);
        $removed = $session_count - $final_count;
        
        echo "<div style='background: " . ($removed > 0 ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($removed > 0 ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($removed > 0 ? '✅' : '❌') . " Database Result</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$removed}</li>\n";
        echo "</ul>\n";
        
        if ($removed > 0) {
            echo "<p><strong>✅ SUCCESS:</strong> The targeted rollback successfully removed {$removed} session(s) from Recent Fixes!</p>\n";
        } else {
            echo "<p><strong>❌ ISSUE:</strong> No sessions were removed from the database.</p>\n";
        }
        echo "</div>\n";
        
        // Overall result
        $overall_success = $is_valid_json && ($response['success'] ?? false) && $removed > 0 && $memory_used < 50 * 1024 * 1024; // Less than 50MB
        
        echo "<div style='background: " . ($overall_success ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($overall_success ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>" . ($overall_success ? '🎉' : '❌') . " Overall Test Result</h3>\n";
        
        if ($overall_success) {
            echo "<p><strong>🎉 COMPLETE SUCCESS!</strong></p>\n";
            echo "<ul>\n";
            echo "<li>✅ Valid JSON response</li>\n";
            echo "<li>✅ Rollback completed successfully</li>\n";
            echo "<li>✅ Session removed from database</li>\n";
            echo "<li>✅ Memory usage under control (" . round($memory_used / 1024 / 1024, 2) . " MB)</li>\n";
            echo "<li>✅ No memory exhaustion errors</li>\n";
            echo "</ul>\n";
            echo "<p>The targeted approach successfully resolved the memory issues while maintaining functionality!</p>\n";
        } else {
            echo "<p><strong>❌ Test Failed</strong></p>\n";
            echo "<p>Check the individual results above to identify the issue.</p>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to verify the Recent Fixes list</li>\n";
    echo "<li>Check if the rolled-back item has been removed from the UI</li>\n";
    echo "<li>Verify that no memory errors occurred</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>🎯 Ready for Targeted Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 About This Targeted Approach</h3>\n";
    echo "<p>This test will verify that the new targeted rollback approach:</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Completes without memory exhaustion</li>\n";
    echo "<li>✅ Successfully removes the session from Recent Fixes</li>\n";
    echo "<li>✅ Uses minimal memory (under 50MB)</li>\n";
    echo "<li>✅ Returns valid JSON response</li>\n";
    echo "<li>✅ Updates the UI properly</li>\n";
    echo "</ul>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🎯 Execute Targeted Rollback Test</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
