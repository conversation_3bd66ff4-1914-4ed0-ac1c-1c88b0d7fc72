<?php
/**
 * Comprehensive Test Script for Diagnostic & Auto-Fix Module Critical Fixes
 * 
 * Tests both:
 * 1. Fixed Issues Reappearing on Page Refresh
 * 2. Missing Rollback Buttons in Recent Fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once dirname(__FILE__) . '/wp-config.php';
}

echo "<h1>🔧 Comprehensive Diagnostic & Auto-Fix Module Test</h1>\n";
echo "<p>Testing critical fixes for Issue #1 (Page Refresh) and Issue #2 (Rollback Buttons)</p>\n";

// Initialize required classes
if (!class_exists('Redco_Diagnostic_AutoFix')) {
    require_once 'modules/diagnostic-autofix/class-diagnostic-autofix.php';
}

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    require_once 'modules/diagnostic-autofix/class-diagnostic-autofix-engine.php';
}

$diagnostic = new Redco_Diagnostic_AutoFix();
$engine = new Redco_Diagnostic_AutoFix_Engine();

echo "<h2>🧪 Test 1: Fixed Issues Filtering on Page Load</h2>\n";

// Create a test issue
$test_issue = array(
    'id' => 'test_security_header_' . time(),
    'title' => 'Missing Security Header (Test)',
    'description' => 'Test security header is missing',
    'severity' => 'medium',
    'category' => 'security',
    'fix_action' => 'add_security_header',
    'auto_fixable' => true,
    'header_name' => 'X-Test-Header',
    'header_value' => 'test-value'
);

echo "📋 Created test issue: {$test_issue['id']}\n";

// Step 1: Add issue to diagnostic results (simulating a scan)
$diagnostic_results = array(
    'timestamp' => time(),
    'issues' => array($test_issue),
    'scan_duration' => 5.2,
    'health_score' => 85
);
update_option('redco_diagnostic_results', $diagnostic_results);
echo "✅ Added test issue to diagnostic results\n";

// Step 2: Apply fix to the issue
echo "\n📝 Applying fix to test issue...\n";
$fix_result = $engine->apply_fix($test_issue);

if ($fix_result['success']) {
    echo "✅ Fix applied successfully: {$fix_result['message']}\n";
    if (isset($fix_result['rollback_id'])) {
        echo "✅ Rollback ID present: {$fix_result['rollback_id']}\n";
    } else {
        echo "❌ Rollback ID missing from fix result\n";
    }
} else {
    echo "❌ Fix failed: {$fix_result['message']}\n";
}

// Step 3: Check if issue is tracked in fixed_issues
echo "\n🔍 Checking fixed issues tracking...\n";
$fixed_issues = get_option('redco_fixed_issues', array());
$issue_id = $test_issue['id'];

if (isset($fixed_issues[$issue_id])) {
    echo "✅ Issue properly tracked in fixed_issues list\n";
    echo "   Timestamp: " . date('Y-m-d H:i:s', $fixed_issues[$issue_id]['timestamp']) . "\n";
} else {
    echo "❌ Issue NOT tracked in fixed_issues list\n";
}

// Step 4: Test page load filtering (simulating tab.php logic)
echo "\n🔄 Testing page load filtering logic...\n";
$last_scan = get_option('redco_diagnostic_results', array());

if (!empty($last_scan['issues'])) {
    $original_count = count($last_scan['issues']);
    echo "Original issues count: {$original_count}\n";
    
    // Apply the same filtering logic as in tab.php
    $fixed_issues = get_option('redco_fixed_issues', array());
    if (!empty($fixed_issues)) {
        $filtered_issues = array();
        foreach ($last_scan['issues'] as $issue) {
            $issue_id = $issue['id'];
            // Check if this issue was previously fixed and is still resolved
            if (isset($fixed_issues[$issue_id])) {
                // Skip this issue - it's been fixed and should not appear
                continue;
            }
            // Include issue in filtered results
            $filtered_issues[] = $issue;
        }
        $last_scan['issues'] = $filtered_issues;
    }
    
    $filtered_count = count($last_scan['issues']);
    echo "Filtered issues count: {$filtered_count}\n";
    
    if ($filtered_count < $original_count) {
        echo "✅ Fixed issues correctly filtered out on page load\n";
    } else {
        echo "❌ Fixed issues NOT filtered on page load\n";
    }
} else {
    echo "❌ No issues found in diagnostic results\n";
}

echo "\n<h2>🔧 Test 2: Rollback Button Availability</h2>\n";

// Step 1: Check fix history
echo "📋 Checking fix history...\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    echo "Latest session details:\n";
    echo "  Timestamp: " . date('Y-m-d H:i:s', $latest_session['timestamp']) . "\n";
    echo "  Fixes applied: " . $latest_session['fixes_applied'] . "\n";
    echo "  Backup created: " . ($latest_session['backup_created'] ? 'Yes' : 'No') . "\n";
    
    if (isset($latest_session['rollback_id']) && !empty($latest_session['rollback_id'])) {
        echo "  ✅ Rollback ID found: " . $latest_session['rollback_id'] . "\n";
    } else {
        echo "  ❌ No rollback ID in latest session\n";
    }
} else {
    echo "❌ No fix history found\n";
}

// Step 2: Test AJAX load recent fixes (simulating the actual call)
echo "\n🔄 Testing AJAX load recent fixes...\n";

// Simulate AJAX request
$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

// Capture output
ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            // Check if rollback button is present
            $rollback_button_count = substr_count($html, 'rollback-fix');
            $unavailable_count = substr_count($html, 'rollback-unavailable');
            
            echo "Rollback buttons found: {$rollback_button_count}\n";
            echo "Unavailable messages found: {$unavailable_count}\n";
            
            if ($rollback_button_count > 0) {
                echo "✅ Rollback button is visible in recent fixes\n";
                
                // Extract rollback ID from button
                if (preg_match('/data-backup-id="([^"]+)"/', $html, $matches)) {
                    $button_backup_id = $matches[1];
                    echo "Button backup ID: " . $button_backup_id . "\n";
                }
            } else {
                echo "❌ No rollback button found in recent fixes HTML\n";
                if ($unavailable_count > 0) {
                    echo "   Reason: Rollback marked as unavailable\n";
                }
            }
        } else {
            echo "❌ AJAX response was not successful\n";
            if (isset($response_data['data'])) {
                echo "   Error: " . $response_data['data'] . "\n";
            }
        }
    } else {
        echo "❌ AJAX call returned no data\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX call failed: " . $e->getMessage() . "\n";
}

echo "\n<h2>📊 Test Summary</h2>\n";

// Summary checks
$all_tests_passed = true;
$test_results = array();

// Check Issue #1: Page load filtering
$fixed_issues = get_option('redco_fixed_issues', array());
if (isset($fixed_issues[$issue_id])) {
    $test_results[] = "✅ Issue tracking works correctly";
} else {
    $test_results[] = "❌ Issue tracking failed";
    $all_tests_passed = false;
}

// Check if filtering worked
$current_scan = get_option('redco_diagnostic_results', array());
$contains_fixed_issue = false;
if (!empty($current_scan['issues'])) {
    foreach ($current_scan['issues'] as $issue) {
        if ($issue['id'] === $issue_id) {
            $contains_fixed_issue = true;
            break;
        }
    }
}

if (!$contains_fixed_issue) {
    $test_results[] = "✅ Page load filtering works correctly";
} else {
    $test_results[] = "❌ Page load filtering failed";
    $all_tests_passed = false;
}

// Check Issue #2: Rollback buttons
$fix_history = get_option('redco_diagnostic_fix_history', array());
$has_rollback_capability = false;
if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    if (isset($latest_session['rollback_id']) && !empty($latest_session['rollback_id'])) {
        $has_rollback_capability = true;
    }
}

if ($has_rollback_capability) {
    $test_results[] = "✅ Rollback capability properly stored";
} else {
    $test_results[] = "❌ Rollback capability missing";
    $all_tests_passed = false;
}

// Display results
foreach ($test_results as $result) {
    echo $result . "\n";
}

if ($all_tests_passed) {
    echo "\n🎉 ALL TESTS PASSED!\n";
    echo "✅ Issue #1: Fixed issues no longer reappear on page refresh\n";
    echo "✅ Issue #2: Rollback buttons are properly available\n";
    echo "✅ Both critical issues have been resolved\n";
} else {
    echo "\n⚠️ SOME TESTS FAILED\n";
    echo "Please check the specific failures above and investigate further.\n";
}

echo "\n<h3>🧹 Cleanup</h3>\n";
echo "Test completed. You may want to remove the test issue from fixed_issues if needed.\n";
echo "Test issue ID: {$issue_id}\n";
