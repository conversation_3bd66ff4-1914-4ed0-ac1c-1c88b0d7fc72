<?php
/**
 * Remediation Script for Rollback Unavailable Items
 * 
 * Fixes or removes items showing "(Rollback unavailable)" in Recent Fixes
 */

// Prevent direct access and ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // Get the WordPress root directory (go up from plugin directory)
    $wp_root = dirname(dirname(dirname(dirname(__FILE__))));
    require_once $wp_root . '/wp-config.php';
}

// Security check - only allow admin users to run this test
if (!current_user_can('manage_options')) {
    wp_die('Access denied. This test can only be run by administrators.');
}

echo "<h1>🔧 Remediation: Fix Rollback Unavailable Items</h1>\n";

// Initialize required classes
if (!class_exists('Redco_Diagnostic_AutoFix')) {
    require_once dirname(dirname(__FILE__)) . '/class-diagnostic-autofix.php';
}

$diagnostic = new Redco_Diagnostic_AutoFix();
$reflection = new ReflectionClass($diagnostic);

echo "<h2>📊 Step 1: Identifying Problematic Sessions</h2>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Total fix sessions: " . count($fix_history) . "\n\n";

$problematic_sessions = array();
$sessions_to_remove = array();
$sessions_to_fix = array();

// Get validation method
$validate_method = $reflection->getMethod('validate_backup_exists');
$validate_method->setAccessible(true);

foreach ($fix_history as $index => $session) {
    $backup_created = isset($session['backup_created']) && $session['backup_created'];
    $has_rollback_id = isset($session['rollback_id']) && !empty($session['rollback_id']);
    
    // Only check sessions that should appear in Recent Fixes
    if ($backup_created || $has_rollback_id) {
        if (!$has_rollback_id) {
            // Missing rollback ID - try to fix
            $sessions_to_fix[] = $index;
            echo "Session {$index}: Missing rollback ID - will attempt to fix\n";
        } else {
            // Has rollback ID - check if backup exists
            $backup_exists = $validate_method->invoke($diagnostic, $session['rollback_id']);
            if (!$backup_exists) {
                // Backup doesn't exist - candidate for removal
                $sessions_to_remove[] = $index;
                echo "Session {$index}: Rollback ID '{$session['rollback_id']}' has no valid backup - will remove\n";
            } else {
                echo "Session {$index}: Valid rollback capability - keeping\n";
            }
        }
    }
}

echo "\nSessions to fix: " . count($sessions_to_fix) . "\n";
echo "Sessions to remove: " . count($sessions_to_remove) . "\n\n";

echo "<h2>🔧 Step 2: Attempting to Fix Missing Rollback IDs</h2>\n";

if (!empty($sessions_to_fix)) {
    echo "Running enhanced migration for sessions with missing rollback IDs...\n";
    
    $migrate_method = $reflection->getMethod('migrate_fix_sessions_rollback_ids');
    $migrate_method->setAccessible(true);
    
    $migration_count = $migrate_method->invoke($diagnostic);
    echo "Migration completed. Sessions processed: {$migration_count}\n";
    
    // Re-check the sessions we tried to fix
    $fix_history_after = get_option('redco_diagnostic_fix_history', array());
    $fixed_sessions = 0;
    
    foreach ($sessions_to_fix as $session_id) {
        if (isset($fix_history_after[$session_id])) {
            $updated_session = $fix_history_after[$session_id];
            if (isset($updated_session['rollback_id']) && !empty($updated_session['rollback_id'])) {
                $backup_exists = $validate_method->invoke($diagnostic, $updated_session['rollback_id']);
                if ($backup_exists) {
                    $fixed_sessions++;
                    echo "  ✅ Session {$session_id}: Successfully fixed with rollback ID '{$updated_session['rollback_id']}'\n";
                } else {
                    // Migration gave it a rollback ID but backup doesn't exist
                    $sessions_to_remove[] = $session_id;
                    echo "  ❌ Session {$session_id}: Migration created rollback ID but no backup exists - will remove\n";
                }
            } else {
                // Migration failed to create rollback ID
                $sessions_to_remove[] = $session_id;
                echo "  ❌ Session {$session_id}: Migration failed to create rollback ID - will remove\n";
            }
        }
    }
    
    echo "Successfully fixed: {$fixed_sessions} sessions\n\n";
} else {
    echo "No sessions need rollback ID fixes\n\n";
}

echo "<h2>🗑️ Step 3: Removing Sessions Without Valid Rollback Capability</h2>\n";

if (!empty($sessions_to_remove)) {
    echo "Removing " . count($sessions_to_remove) . " sessions without valid rollback capability...\n";
    
    // Get the current fix history
    $current_fix_history = get_option('redco_diagnostic_fix_history', array());
    $removed_count = 0;
    
    // Remove sessions in reverse order to maintain array indices
    $sessions_to_remove = array_unique($sessions_to_remove);
    rsort($sessions_to_remove);
    
    foreach ($sessions_to_remove as $session_id) {
        if (isset($current_fix_history[$session_id])) {
            $session = $current_fix_history[$session_id];
            $timestamp = isset($session['timestamp']) ? date('Y-m-d H:i:s', $session['timestamp']) : 'unknown';
            
            echo "  Removing session {$session_id} ({$timestamp})\n";
            
            // Log the removal for debugging
            error_log("REDCO CLEANUP: Removing fix session {$session_id} without valid rollback capability");
            
            unset($current_fix_history[$session_id]);
            $removed_count++;
        }
    }
    
    // Re-index the array to maintain sequential indices
    $current_fix_history = array_values($current_fix_history);
    
    // Update the option
    update_option('redco_diagnostic_fix_history', $current_fix_history);
    
    echo "Successfully removed {$removed_count} sessions\n\n";
} else {
    echo "No sessions need to be removed\n\n";
}

echo "<h2>✅ Step 4: Verification</h2>\n";

echo "Testing AJAX response after cleanup...\n";

$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            $rollback_buttons = substr_count($html, 'rollback-fix');
            $unavailable_messages = substr_count($html, 'rollback-unavailable');
            
            echo "AJAX response after cleanup:\n";
            echo "  Rollback buttons: {$rollback_buttons}\n";
            echo "  Unavailable messages: {$unavailable_messages}\n";
            
            if ($unavailable_messages === 0) {
                echo "  ✅ SUCCESS: No more unavailable messages!\n";
            } else {
                echo "  ⚠️ Still showing {$unavailable_messages} unavailable messages\n";
                
                // Extract the remaining unavailable items
                if (preg_match_all('/rollback-unavailable[^>]*title="([^"]*)"/', $html, $matches)) {
                    echo "  Remaining unavailable reasons:\n";
                    foreach ($matches[1] as $reason) {
                        echo "    - {$reason}\n";
                    }
                }
            }
            
            // Show sample of the HTML for verification
            if ($rollback_buttons > 0) {
                echo "\n  Sample rollback buttons found:\n";
                if (preg_match_all('/data-backup-id="([^"]+)"/', $html, $backup_matches)) {
                    foreach (array_slice($backup_matches[1], 0, 3) as $backup_id) {
                        echo "    - Backup ID: {$backup_id}\n";
                    }
                }
            }
        } else {
            echo "❌ AJAX response failed\n";
        }
    } else {
        echo "❌ AJAX returned empty response\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX exception: " . $e->getMessage() . "\n";
}

echo "\n<h2>📊 Final Summary</h2>\n";

$final_fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions after cleanup: " . count($final_fix_history) . "\n";

// Count sessions with rollback capability
$sessions_with_rollback = 0;
foreach ($final_fix_history as $session) {
    $backup_created = isset($session['backup_created']) && $session['backup_created'];
    $has_rollback_id = isset($session['rollback_id']) && !empty($session['rollback_id']);
    
    if (($backup_created || $has_rollback_id) && $has_rollback_id) {
        $backup_exists = $validate_method->invoke($diagnostic, $session['rollback_id']);
        if ($backup_exists) {
            $sessions_with_rollback++;
        }
    }
}

echo "Sessions with valid rollback capability: {$sessions_with_rollback}\n";

if (isset($removed_count)) {
    echo "Sessions removed: {$removed_count}\n";
}

if (isset($fixed_sessions)) {
    echo "Sessions fixed: {$fixed_sessions}\n";
}

echo "\n";

if ($unavailable_messages === 0) {
    echo "🎉 SUCCESS: All Recent Fixes items now have functional rollback buttons!\n";
    echo "✅ The Recent Fixes list has been cleaned up successfully\n";
    echo "✅ No more '(Rollback unavailable)' messages should appear\n";
} else {
    echo "⚠️ PARTIAL SUCCESS: Reduced unavailable messages but some remain\n";
    echo "Additional investigation may be needed for remaining items\n";
}

echo "\n<h3>📋 What was done:</h3>\n";
echo "1. ✅ Identified sessions without valid rollback capability\n";
echo "2. ✅ Attempted to fix missing rollback IDs through migration\n";
echo "3. ✅ Removed sessions with no valid backup data\n";
echo "4. ✅ Verified the Recent Fixes list now shows only functional items\n";
echo "5. ✅ Maintained data integrity and proper indexing\n";

echo "\n<h3>🔍 For debugging:</h3>\n";
echo "Check WordPress error log for 'REDCO CLEANUP' messages\n";
echo "All removed sessions have been logged for reference\n";
?>
