<?php
/**
 * Comprehensive Cleanup Verification Test
 * Tests the fixes for stats update and .htaccess file removal
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🔍 Comprehensive Cleanup Verification Test</h1>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🎯 Testing Both Fixes</h3>\n";
echo "<p>This test verifies the fixes for:</p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Stats Update Issue</strong> - Enhanced logging and verification</li>\n";
echo "<li>✅ <strong>.htaccess File Removal</strong> - Multiple file patterns support</li>\n";
echo "<li>✅ <strong>Backup Folder Cleanup</strong> - Directory removal</li>\n";
echo "<li>✅ <strong>Recent Fixes Update</strong> - Database session removal</li>\n";
echo "</ul>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_08-52-41_6842ac593eed8';

// Get current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$current_stats = get_option('redco_diagnostic_stats', array());
$session_count = count($fix_history);

echo "<h2>📊 Pre-Test State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Current Fixes Applied (Stats):</strong> " . ($current_stats['fixes_applied'] ?? 0) . "</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Create test files for comprehensive testing
echo "<h2>🔧 Creating Test Files</h2>\n";

$backup_dir = WP_CONTENT_DIR . '/uploads/redco-backups/' . $specific_backup_id;
$test_files_created = array();

// 1. Create backup directory
if (!is_dir($backup_dir)) {
    wp_mkdir_p($backup_dir);
    file_put_contents($backup_dir . '/test_file.txt', 'Test backup content');
    $test_files_created[] = "Backup directory: {$backup_dir}";
}

// 2. Create multiple .htaccess backup files (using correct engine patterns)
$timestamp = time();
$htaccess_files = array(
    ABSPATH . '.htaccess.redco-backup-' . $timestamp,
    ABSPATH . '.htaccess.redco-backup-' . ($timestamp + 1) . '-abc123',
    ABSPATH . '.htaccess.redco-backup-' . ($timestamp + 2) . '-xyz789'
);

foreach ($htaccess_files as $file) {
    if (!file_exists($file)) {
        file_put_contents($file, '# Test backup .htaccess file - ' . basename($file));
        $test_files_created[] = "Backup .htaccess: " . basename($file);
    }
}

// 3. Create test session if needed
$backup_found = false;
foreach ($fix_history as $session) {
    if (($session['rollback_id'] ?? '') === $specific_backup_id || 
        ($session['backup_id'] ?? '') === $specific_backup_id) {
        $backup_found = true;
        break;
    }
}

if (!$backup_found) {
    $test_session = array(
        'session_id' => 'verification_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Comprehensive verification test session',
        'fixes_applied' => 1,
        'backup_created' => true
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    // Update stats to reflect the added fix
    $current_stats['fixes_applied'] = ($current_stats['fixes_applied'] ?? 0) + 1;
    update_option('redco_diagnostic_stats', $current_stats);
    
    $test_files_created[] = "Test session in fix history";
    $session_count = count($fix_history);
}

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>✅ Test Files Created</h3>\n";
echo "<ul>\n";
foreach ($test_files_created as $file) {
    echo "<li>{$file}</li>\n";
}
echo "</ul>\n";
echo "</div>\n";

// Execute comprehensive verification test
if (isset($_GET['execute'])) {
    echo "<h2>🧪 Executing Comprehensive Verification Test</h2>\n";
    
    $initial_memory = memory_get_usage();
    $pre_stats = get_option('redco_diagnostic_stats', array());
    $pre_fixes_count = $pre_stats['fixes_applied'] ?? 0;
    
    // Count existing .htaccess backup files
    $pre_htaccess_files = array_merge(
        glob(ABSPATH . '.htaccess.backup.*'),
        glob(ABSPATH . '.htaccess.redco-backup-*')
    );
    $pre_htaccess_count = count($pre_htaccess_files);
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Rollback with Enhanced Cleanup</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Stats Fixes Before:</strong> {$pre_fixes_count}</li>\n";
        echo "<li><strong>.htaccess Files Before:</strong> {$pre_htaccess_count}</li>\n";
        echo "<li><strong>Memory Before:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Execute rollback
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        $final_memory = memory_get_usage();
        $memory_used = $final_memory - $initial_memory;
        
        // Parse response
        $response = json_decode($output, true);
        $is_valid_json = ($response !== null);
        
        echo "<div style='background: " . ($is_valid_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_valid_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_valid_json ? '✅' : '❌') . " AJAX Response</h3>\n";
        
        if ($is_valid_json && ($response['success'] ?? false)) {
            echo "<p><strong>✅ SUCCESS:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
            
            if (isset($response['data']['cleanup_performed']['details'])) {
                $cleanup = $response['data']['cleanup_performed']['details'];
                echo "<h4>🧹 Cleanup Results:</h4>\n";
                echo "<ul>\n";
                if (!empty($cleanup['completed'])) {
                    foreach ($cleanup['completed'] as $task) {
                        echo "<li>✅ {$task}</li>\n";
                    }
                }
                if (!empty($cleanup['failed'])) {
                    foreach ($cleanup['failed'] as $task) {
                        echo "<li>❌ {$task}</li>\n";
                    }
                }
                echo "</ul>\n";
            }
        } else {
            echo "<p><strong>❌ FAILED:</strong> " . htmlspecialchars($response['data'] ?? $output) . "</p>\n";
        }
        echo "</div>\n";
        
        // Comprehensive verification
        echo "<h3>🔍 Comprehensive Verification</h3>\n";
        
        // 1. Check Recent Fixes update
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_history);
        $sessions_removed = $session_count - $final_count;
        
        // 2. Check stats update
        $post_stats = get_option('redco_diagnostic_stats', array());
        $post_fixes_count = $post_stats['fixes_applied'] ?? 0;
        $stats_decremented = ($pre_fixes_count - $post_fixes_count);
        
        // 3. Check backup directory
        $post_backup_dir_exists = is_dir($backup_dir);
        
        // 4. Check .htaccess files
        $post_htaccess_files = array_merge(
            glob(ABSPATH . '.htaccess.backup.*'),
            glob(ABSPATH . '.htaccess.redco-backup-*')
        );
        $post_htaccess_count = count($post_htaccess_files);
        $htaccess_files_removed = $pre_htaccess_count - $post_htaccess_count;
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📊 Detailed Verification Results</h4>\n";
        echo "<table style='width: 100%; border-collapse: collapse;'>\n";
        echo "<tr><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Component</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Before</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>After</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Change</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Status</th></tr>\n";
        
        // Recent Fixes
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Recent Fixes Sessions</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$session_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$final_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>-{$sessions_removed}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($sessions_removed > 0 ? '✅ Updated' : '❌ Not updated') . "</td></tr>\n";
        
        // Stats
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Stats Fixes Count</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$pre_fixes_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$post_fixes_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>-{$stats_decremented}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($stats_decremented > 0 ? '✅ Updated' : '❌ Not updated') . "</td></tr>\n";
        
        // Backup Directory
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Backup Directory</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>EXISTS</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_backup_dir_exists ? 'EXISTS' : 'REMOVED') . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_backup_dir_exists ? 'No change' : 'Removed') . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . (!$post_backup_dir_exists ? '✅ Cleaned' : '❌ Still exists') . "</td></tr>\n";
        
        // .htaccess Files
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>.htaccess Backup Files</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$pre_htaccess_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$post_htaccess_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>-{$htaccess_files_removed}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($htaccess_files_removed > 0 ? '✅ Cleaned' : ($post_htaccess_count == 0 ? '✅ None found' : '❌ Not cleaned')) . "</td></tr>\n";
        
        // Memory
        echo "<tr><td style='padding: 8px;'>Memory Usage</td><td style='padding: 8px;'>" . round($initial_memory / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>" . round($final_memory / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>+" . round($memory_used / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>" . ($memory_used < 10 * 1024 * 1024 ? '✅ Efficient' : '⚠️ High') . "</td></tr>\n";
        
        echo "</table>\n";
        echo "</div>\n";
        
        // Overall success analysis
        $recent_fixes_ok = $sessions_removed > 0;
        $stats_ok = $stats_decremented > 0;
        $backup_dir_ok = !$post_backup_dir_exists;
        $htaccess_ok = ($htaccess_files_removed > 0 || $post_htaccess_count == 0);
        $memory_ok = $memory_used < 10 * 1024 * 1024;
        
        $all_issues_fixed = $recent_fixes_ok && $stats_ok && $backup_dir_ok && $htaccess_ok && $memory_ok;
        
        echo "<div style='background: " . ($all_issues_fixed ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($all_issues_fixed ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>" . ($all_issues_fixed ? '🎉' : '⚠️') . " Final Verification Result</h3>\n";
        
        if ($all_issues_fixed) {
            echo "<p><strong>🎉 ALL ISSUES COMPLETELY RESOLVED!</strong></p>\n";
            echo "<p>Both identified problems have been successfully fixed:</p>\n";
            echo "<ul>\n";
            echo "<li>✅ Recent Fixes list properly updated</li>\n";
            echo "<li>✅ Backup folder removed from redco-backups</li>\n";
            echo "<li>✅ Backup .htaccess files removed (all patterns)</li>\n";
            echo "<li>✅ Stats updated and decremented correctly</li>\n";
            echo "<li>✅ Memory usage under control</li>\n";
            echo "</ul>\n";
            echo "<p><strong>The rollback functionality is now working perfectly!</strong></p>\n";
        } else {
            echo "<p><strong>⚠️ Some Issues Still Need Attention</strong></p>\n";
            echo "<ul>\n";
            echo "<li>" . ($recent_fixes_ok ? '✅' : '❌') . " Recent Fixes list updated</li>\n";
            echo "<li>" . ($stats_ok ? '✅' : '❌') . " Stats updated correctly</li>\n";
            echo "<li>" . ($backup_dir_ok ? '✅' : '❌') . " Backup folder removed</li>\n";
            echo "<li>" . ($htaccess_ok ? '✅' : '❌') . " .htaccess backup files removed</li>\n";
            echo "<li>" . ($memory_ok ? '✅' : '❌') . " Memory efficient</li>\n";
            echo "</ul>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Verify that the Recent Fixes list reflects the changes</li>\n";
    echo "<li>Check that no memory errors occurred in the error logs</li>\n";
    echo "<li>Confirm that all cleanup operations completed successfully</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>🧪 Ready for Comprehensive Verification</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 What This Test Will Verify</h3>\n";
    echo "<p>This comprehensive test will verify that ALL identified problems are resolved:</p>\n";
    echo "<ol>\n";
    echo "<li>✅ <strong>Recent Fixes List</strong> - Session removed from database</li>\n";
    echo "<li>✅ <strong>Backup Folder</strong> - Directory removed from redco-backups</li>\n";
    echo "<li>✅ <strong>.htaccess Files</strong> - All backup patterns removed (fixed)</li>\n";
    echo "<li>✅ <strong>Stats Update</strong> - Fixes count decremented properly (fixed)</li>\n";
    echo "<li>✅ <strong>Memory Efficiency</strong> - Under 10MB usage</li>\n";
    echo "</ol>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Execute Comprehensive Verification</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='memory-efficient-cleanup-test.php'>🧹 Memory-Efficient Cleanup Test</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
