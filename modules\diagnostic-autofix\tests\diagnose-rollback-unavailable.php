<?php
/**
 * Diagnostic Script for Rollback Unavailable Items
 * 
 * Analyzes the specific items showing "(Rollback unavailable)" in Recent Fixes
 */

// Prevent direct access and ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // Get the WordPress root directory (go up from plugin directory)
    $wp_root = dirname(dirname(dirname(dirname(__FILE__))));
    require_once $wp_root . '/wp-config.php';
}

// Security check - only allow admin users to run this test
if (!current_user_can('manage_options')) {
    wp_die('Access denied. This test can only be run by administrators.');
}

echo "<h1>🔍 Diagnostic Analysis: Rollback Unavailable Items</h1>\n";

// Initialize required classes
if (!class_exists('Redco_Diagnostic_AutoFix')) {
    require_once dirname(dirname(__FILE__)) . '/class-diagnostic-autofix.php';
}

$diagnostic = new Redco_Diagnostic_AutoFix();

echo "<h2>📊 Step 1: Examining Fix History Data</h2>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Total fix sessions: " . count($fix_history) . "\n\n";

if (empty($fix_history)) {
    echo "❌ No fix history found\n";
    exit;
}

// Analyze each session to identify problematic ones
$problematic_sessions = array();
$working_sessions = array();

foreach ($fix_history as $index => $session) {
    $session_id = $index;
    $timestamp = isset($session['timestamp']) ? $session['timestamp'] : 0;
    $backup_created = isset($session['backup_created']) && $session['backup_created'];
    $has_rollback_id = isset($session['rollback_id']) && !empty($session['rollback_id']);
    
    echo "Session {$session_id} (" . date('Y-m-d H:i:s', $timestamp) . "):\n";
    echo "  backup_created: " . ($backup_created ? 'YES' : 'NO') . "\n";
    echo "  rollback_id: " . ($has_rollback_id ? $session['rollback_id'] : 'NOT SET') . "\n";
    
    if ($backup_created || $has_rollback_id) {
        // This session should show in Recent Fixes
        if ($has_rollback_id) {
            // Test backup validation
            $reflection = new ReflectionClass($diagnostic);
            $validate_method = $reflection->getMethod('validate_backup_exists');
            $validate_method->setAccessible(true);
            
            $backup_exists = $validate_method->invoke($diagnostic, $session['rollback_id']);
            echo "  backup_validation: " . ($backup_exists ? 'VALID' : 'INVALID') . "\n";
            
            if (!$backup_exists) {
                $problematic_sessions[] = array(
                    'session_id' => $session_id,
                    'session' => $session,
                    'reason' => 'backup_validation_failed'
                );
                echo "  ❌ PROBLEMATIC: Backup validation failed\n";
            } else {
                $working_sessions[] = $session_id;
                echo "  ✅ WORKING: Backup validation passed\n";
            }
        } else {
            $problematic_sessions[] = array(
                'session_id' => $session_id,
                'session' => $session,
                'reason' => 'missing_rollback_id'
            );
            echo "  ❌ PROBLEMATIC: Missing rollback ID\n";
        }
    } else {
        echo "  ℹ️ SKIPPED: No backup indication\n";
    }
    echo "\n";
}

echo "<h2>🔍 Step 2: Detailed Analysis of Problematic Sessions</h2>\n";

if (empty($problematic_sessions)) {
    echo "✅ No problematic sessions found!\n";
} else {
    echo "Found " . count($problematic_sessions) . " problematic sessions:\n\n";
    
    foreach ($problematic_sessions as $prob) {
        $session_id = $prob['session_id'];
        $session = $prob['session'];
        $reason = $prob['reason'];
        
        echo "🚨 Problematic Session {$session_id}:\n";
        echo "  Reason: {$reason}\n";
        echo "  Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "\n";
        echo "  Session Data:\n";
        
        // Show all session fields
        foreach ($session as $key => $value) {
            if (is_array($value)) {
                echo "    {$key}: " . count($value) . " items\n";
                if ($key === 'details' && !empty($value)) {
                    foreach ($value as $detail_index => $detail) {
                        echo "      Detail {$detail_index}:\n";
                        foreach ($detail as $detail_key => $detail_value) {
                            if (is_string($detail_value) || is_numeric($detail_value)) {
                                echo "        {$detail_key}: {$detail_value}\n";
                            }
                        }
                    }
                }
            } else {
                $display_value = is_bool($value) ? ($value ? 'true' : 'false') : $value;
                echo "    {$key}: {$display_value}\n";
            }
        }
        
        // If there's a rollback_id, check all storage locations
        if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
            $rollback_id = $session['rollback_id'];
            echo "\n  🔍 Checking backup storage for: {$rollback_id}\n";
            
            // Get possible backup directories
            $get_backup_dirs_method = $reflection->getMethod('get_possible_backup_directories');
            $get_backup_dirs_method->setAccessible(true);
            $backup_dirs = $get_backup_dirs_method->invoke($diagnostic);
            
            foreach ($backup_dirs as $dir) {
                echo "    Directory: {$dir}\n";
                if (is_dir($dir)) {
                    $backup_subdir = $dir . $rollback_id . '/';
                    $backup_file = $dir . $rollback_id . '.json';
                    
                    echo "      Subdirectory: " . (is_dir($backup_subdir) ? 'EXISTS' : 'NOT FOUND') . "\n";
                    if (is_dir($backup_subdir)) {
                        $metadata_file = $backup_subdir . 'backup_data.json';
                        echo "        Metadata: " . (file_exists($metadata_file) ? 'EXISTS' : 'NOT FOUND') . "\n";
                        if (file_exists($metadata_file)) {
                            $metadata_size = filesize($metadata_file);
                            echo "        Size: {$metadata_size} bytes\n";
                        }
                    }
                    
                    echo "      JSON file: " . (file_exists($backup_file) ? 'EXISTS' : 'NOT FOUND') . "\n";
                } else {
                    echo "      Directory does not exist\n";
                }
            }
            
            // Check options table
            echo "    Options table:\n";
            $backup_option_key = 'redco_backup_' . $rollback_id;
            $backup_data = get_option($backup_option_key);
            echo "      redco_backup_: " . (!empty($backup_data) ? 'FOUND' : 'NOT FOUND') . "\n";
            
            $optimization_backup_key = 'redco_optimization_backup_' . $rollback_id;
            $optimization_backup = get_option($optimization_backup_key);
            echo "      redco_optimization_backup_: " . (!empty($optimization_backup) ? 'FOUND' : 'NOT FOUND') . "\n";
        }
        
        echo "\n";
    }
}

echo "<h2>🔧 Step 3: Testing Migration Function</h2>\n";

echo "Running migration function to attempt fixing missing rollback IDs...\n";
$migrate_method = $reflection->getMethod('migrate_fix_sessions_rollback_ids');
$migrate_method->setAccessible(true);

$migration_count = $migrate_method->invoke($diagnostic);
echo "Migration completed. Sessions migrated: {$migration_count}\n";

if ($migration_count > 0) {
    echo "Re-checking problematic sessions after migration...\n\n";
    
    $fix_history_after = get_option('redco_diagnostic_fix_history', array());
    
    foreach ($problematic_sessions as $prob) {
        $session_id = $prob['session_id'];
        if (isset($fix_history_after[$session_id])) {
            $updated_session = $fix_history_after[$session_id];
            echo "Session {$session_id} after migration:\n";
            echo "  rollback_id: " . (isset($updated_session['rollback_id']) ? $updated_session['rollback_id'] : 'STILL NOT SET') . "\n";
            
            if (isset($updated_session['rollback_id'])) {
                $backup_exists = $validate_method->invoke($diagnostic, $updated_session['rollback_id']);
                echo "  backup_validation: " . ($backup_exists ? 'VALID' : 'INVALID') . "\n";
            }
            echo "\n";
        }
    }
}

echo "<h2>📋 Step 4: Recommendations</h2>\n";

$recommendations = array();

if (!empty($problematic_sessions)) {
    foreach ($problematic_sessions as $prob) {
        $session_id = $prob['session_id'];
        $reason = $prob['reason'];
        
        if ($reason === 'missing_rollback_id') {
            $recommendations[] = "Session {$session_id}: Generate or find rollback ID";
        } elseif ($reason === 'backup_validation_failed') {
            $recommendations[] = "Session {$session_id}: Fix backup validation or remove from Recent Fixes";
        }
    }
    
    echo "Recommended actions:\n";
    foreach ($recommendations as $rec) {
        echo "  - {$rec}\n";
    }
} else {
    echo "✅ No problematic sessions found. All Recent Fixes should have working rollback buttons.\n";
}

echo "\n<h2>🧪 Step 5: Testing Current AJAX Response</h2>\n";

echo "Testing current AJAX load recent fixes response...\n";

$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            $rollback_buttons = substr_count($html, 'rollback-fix');
            $unavailable_messages = substr_count($html, 'rollback-unavailable');
            
            echo "Current AJAX response:\n";
            echo "  Rollback buttons: {$rollback_buttons}\n";
            echo "  Unavailable messages: {$unavailable_messages}\n";
            
            if ($unavailable_messages > 0) {
                echo "  ❌ Still showing {$unavailable_messages} unavailable messages\n";
                
                // Extract the unavailable items for analysis
                if (preg_match_all('/rollback-unavailable[^>]*title="([^"]*)"/', $html, $matches)) {
                    echo "  Unavailable reasons:\n";
                    foreach ($matches[1] as $reason) {
                        echo "    - {$reason}\n";
                    }
                }
            } else {
                echo "  ✅ No unavailable messages found\n";
            }
        } else {
            echo "❌ AJAX response failed\n";
        }
    } else {
        echo "❌ AJAX returned empty response\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX exception: " . $e->getMessage() . "\n";
}

echo "\n<h3>📊 Summary</h3>\n";
echo "Problematic sessions found: " . count($problematic_sessions) . "\n";
echo "Working sessions: " . count($working_sessions) . "\n";
echo "Migration attempts: {$migration_count}\n";

if (!empty($problematic_sessions)) {
    echo "\n⚠️ Action required: " . count($problematic_sessions) . " sessions need attention\n";
} else {
    echo "\n✅ All sessions appear to be working correctly\n";
}
?>
