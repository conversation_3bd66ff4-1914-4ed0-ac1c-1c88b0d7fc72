<?php
/**
 * Simple test to verify session recording works correctly without duplicates
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

echo "=== SESSION RECORDING TEST ===\n";

// Clear existing fix history
delete_option('redco_diagnostic_fix_history');
echo "Cleared existing fix history\n";

// Test multiple fix applications to ensure no duplicates
$test_issues = array(
    array(
        'id' => 'test_1',
        'title' => 'Test Issue 1',
        'fix_action' => 'enable_compression',
        'auto_fixable' => true
    ),
    array(
        'id' => 'test_2', 
        'title' => 'Test Issue 2',
        'fix_action' => 'enable_compression',
        'auto_fixable' => true
    )
);

echo "\nApplying " . count($test_issues) . " test fixes...\n";

foreach ($test_issues as $index => $issue) {
    echo "\n--- Applying Fix " . ($index + 1) . " ---\n";
    
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    $result = $engine->apply_fix($issue);
    
    echo "Fix success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
    echo "Rollback ID: " . ($result['rollback_id'] ?? 'NONE') . "\n";
    
    // Check fix history after each fix
    $fix_history = get_option('redco_diagnostic_fix_history', array());
    echo "Fix history count: " . count($fix_history) . "\n";
    
    // Verify the expected count
    $expected_count = $index + 1;
    if (count($fix_history) === $expected_count) {
        echo "✅ Correct session count ($expected_count)\n";
    } else {
        echo "❌ Incorrect session count! Expected $expected_count, got " . count($fix_history) . "\n";
    }
}

// Final verification
echo "\n=== FINAL VERIFICATION ===\n";
$final_history = get_option('redco_diagnostic_fix_history', array());
echo "Total sessions recorded: " . count($final_history) . "\n";
echo "Total fixes applied: " . count($test_issues) . "\n";

if (count($final_history) === count($test_issues)) {
    echo "✅ SUCCESS: No duplicate sessions detected!\n";
    echo "Each fix created exactly one session.\n";
} else {
    echo "❌ FAILURE: Duplicate sessions detected!\n";
    echo "Expected " . count($test_issues) . " sessions, found " . count($final_history) . "\n";
}

// Show session details
echo "\nSession details:\n";
foreach ($final_history as $index => $session) {
    echo "Session " . ($index + 1) . ":\n";
    echo "  - Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "\n";
    echo "  - Fixes applied: " . $session['fixes_applied'] . "\n";
    echo "  - Rollback ID: " . ($session['rollback_id'] ?? 'NONE') . "\n";
    echo "  - Details count: " . count($session['details'] ?? array()) . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
