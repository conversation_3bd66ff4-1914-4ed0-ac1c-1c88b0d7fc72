<?php
/**
 * Final cleanup of .htaccess file to remove any remaining security header references
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== FINAL .HTACCESS CLEANUP ===\n";

$htaccess_file = ABSPATH . '.htaccess';

if (file_exists($htaccess_file)) {
    $content = file_get_contents($htaccess_file);
    $original_content = $content;
    
    // Remove any remaining security header references
    $patterns = array(
        '/# Security Headers - Added by Redco Optimizer\s*\n/',
        '/# BEGIN REDCO Security Headers.*?# END REDCO Security Headers\s*/s',
        '/Header always set X-Content-Type-Options.*?\n/i',
        '/Header always set X-Frame-Options.*?\n/i',
        '/Header always set X-XSS-Protection.*?\n/i',
        '/Header always set Referrer-Policy.*?\n/i',
        '/Header always set Strict-Transport-Security.*?\n/i'
    );
    
    foreach ($patterns as $pattern) {
        $content = preg_replace($pattern, '', $content);
    }
    
    // Clean up any double newlines
    $content = preg_replace('/\n\n+/', "\n\n", $content);
    
    if ($content !== $original_content) {
        if (file_put_contents($htaccess_file, $content, LOCK_EX) !== false) {
            echo "✅ Cleaned remaining security header references from .htaccess\n";
        } else {
            echo "❌ Failed to update .htaccess file\n";
        }
    } else {
        echo "ℹ️  No security header references found to clean\n";
    }
} else {
    echo "ℹ️  .htaccess file not found\n";
}

echo "=== .HTACCESS CLEANUP COMPLETE ===\n";
