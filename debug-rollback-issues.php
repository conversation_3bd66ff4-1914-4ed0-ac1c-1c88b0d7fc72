<?php
/**
 * Deep Debug Script for Rollback Button Issues
 * 
 * This script will thoroughly investigate why rollback buttons are still showing as unavailable
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once dirname(__FILE__) . '/wp-config.php';
}

echo "<h1>🔍 Deep Debug: Rollback Button Issues</h1>\n";

// Initialize required classes
if (!class_exists('Redco_Diagnostic_AutoFix')) {
    require_once 'modules/diagnostic-autofix/class-diagnostic-autofix.php';
}

$diagnostic = new Redco_Diagnostic_AutoFix();

echo "<h2>📊 Step 1: Examining Fix History Data Structure</h2>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n\n";

if (!empty($fix_history)) {
    echo "<h3>Recent Fix Sessions Analysis:</h3>\n";
    
    // Analyze last 5 sessions
    $recent_sessions = array_slice(array_reverse($fix_history), 0, 5);
    
    foreach ($recent_sessions as $index => $session) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>Session " . ($index + 1) . " (Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . ")</h4>\n";
        
        // Check all relevant fields
        $fields_to_check = array('backup_created', 'rollback_id', 'backup_id', 'fixes_applied', 'details');
        
        foreach ($fields_to_check as $field) {
            if (isset($session[$field])) {
                if ($field === 'details' && is_array($session[$field])) {
                    echo "✅ {$field}: " . count($session[$field]) . " items\n";
                    // Check if details contain rollback_id
                    foreach ($session[$field] as $detail_index => $detail) {
                        if (isset($detail['rollback_id'])) {
                            echo "   Detail {$detail_index} rollback_id: {$detail['rollback_id']}\n";
                        }
                    }
                } else {
                    $value = is_bool($session[$field]) ? ($session[$field] ? 'true' : 'false') : $session[$field];
                    echo "✅ {$field}: {$value}\n";
                }
            } else {
                echo "❌ {$field}: NOT SET\n";
            }
        }
        
        echo "</div>\n";
    }
} else {
    echo "❌ No fix history found\n";
}

echo "\n<h2>🔧 Step 2: Testing Migration Function</h2>\n";

// Test the migration function directly
echo "Testing migrate_fix_sessions_rollback_ids() function...\n";

// Use reflection to access the private method
$reflection = new ReflectionClass($diagnostic);
$migrate_method = $reflection->getMethod('migrate_fix_sessions_rollback_ids');
$migrate_method->setAccessible(true);

echo "Calling migration function...\n";
$migrate_method->invoke($diagnostic);
echo "Migration function completed.\n\n";

// Check if anything changed
$fix_history_after = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after migration: " . count($fix_history_after) . "\n";

if (count($fix_history_after) > 0) {
    echo "<h3>Sessions After Migration:</h3>\n";
    $recent_sessions_after = array_slice(array_reverse($fix_history_after), 0, 3);
    
    foreach ($recent_sessions_after as $index => $session) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>Session " . ($index + 1) . " After Migration</h4>\n";
        
        $has_backup = isset($session['backup_created']) && $session['backup_created'];
        $has_rollback_id = isset($session['rollback_id']) && !empty($session['rollback_id']);
        
        echo "backup_created: " . ($has_backup ? 'true' : 'false') . "\n";
        echo "rollback_id: " . ($has_rollback_id ? $session['rollback_id'] : 'NOT SET') . "\n";
        echo "</div>\n";
    }
}

echo "\n<h2>🗄️ Step 3: Testing Backup Validation</h2>\n";

// Test backup validation for existing rollback IDs
$validate_method = $reflection->getMethod('validate_backup_exists');
$validate_method->setAccessible(true);

if (!empty($fix_history_after)) {
    foreach (array_slice(array_reverse($fix_history_after), 0, 3) as $index => $session) {
        if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
            $rollback_id = $session['rollback_id'];
            echo "Testing backup validation for: {$rollback_id}\n";
            
            $backup_exists = $validate_method->invoke($diagnostic, $rollback_id);
            echo "Backup exists: " . ($backup_exists ? 'YES' : 'NO') . "\n";
            
            if (!$backup_exists) {
                echo "Checking specific storage locations:\n";
                
                // Check options table
                $backup_option_key = 'redco_backup_' . $rollback_id;
                $backup_data = get_option($backup_option_key);
                echo "  Options table (redco_backup_): " . (!empty($backup_data) ? 'FOUND' : 'NOT FOUND') . "\n";
                
                $optimization_backup_key = 'redco_optimization_backup_' . $rollback_id;
                $optimization_backup = get_option($optimization_backup_key);
                echo "  Options table (redco_optimization_backup_): " . (!empty($optimization_backup) ? 'FOUND' : 'NOT FOUND') . "\n";
                
                // Check filesystem
                $backup_dir = WP_CONTENT_DIR . '/redco-backups/';
                echo "  Backup directory exists: " . (is_dir($backup_dir) ? 'YES' : 'NO') . "\n";
                
                if (is_dir($backup_dir)) {
                    $backup_file = $backup_dir . $rollback_id . '.json';
                    echo "  Backup file (.json): " . (file_exists($backup_file) ? 'FOUND' : 'NOT FOUND') . "\n";
                    
                    $backup_subdir = $backup_dir . $rollback_id . '/';
                    echo "  Backup subdirectory: " . (is_dir($backup_subdir) ? 'FOUND' : 'NOT FOUND') . "\n";
                }
            }
            echo "\n";
        }
    }
}

echo "\n<h2>🎭 Step 4: Simulating AJAX Load Recent Fixes</h2>\n";

// Simulate the actual AJAX call that generates the HTML
echo "Simulating ajax_load_recent_fixes() call...\n";

// Set up AJAX environment
$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

// Capture the output
ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            echo "AJAX call successful. Analyzing HTML output...\n\n";
            
            // Count different elements
            $rollback_button_count = substr_count($html, 'rollback-fix');
            $unavailable_count = substr_count($html, 'rollback-unavailable');
            $backup_created_count = substr_count($html, 'Backup created');
            
            echo "HTML Analysis:\n";
            echo "  Rollback buttons: {$rollback_button_count}\n";
            echo "  Unavailable messages: {$unavailable_count}\n";
            echo "  'Backup created' text: {$backup_created_count}\n\n";
            
            // Extract specific rollback IDs from buttons
            if (preg_match_all('/data-backup-id="([^"]+)"/', $html, $matches)) {
                echo "Rollback IDs found in buttons:\n";
                foreach ($matches[1] as $backup_id) {
                    echo "  - {$backup_id}\n";
                }
            } else {
                echo "No rollback IDs found in HTML\n";
            }
            
            // Show a sample of the HTML for manual inspection
            echo "\nSample HTML output (first 500 chars):\n";
            echo "<pre>" . htmlspecialchars(substr($html, 0, 500)) . "...</pre>\n";
            
        } else {
            echo "❌ AJAX call failed\n";
            if (isset($response_data['data'])) {
                echo "Error: " . $response_data['data'] . "\n";
            }
        }
    } else {
        echo "❌ AJAX call returned empty response\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX call threw exception: " . $e->getMessage() . "\n";
}

echo "\n<h2>🔍 Step 5: Checking All Backup Storage Locations</h2>\n";

echo "Scanning all possible backup storage locations...\n\n";

// Check options table for all backup-related options
global $wpdb;
$backup_options = $wpdb->get_results("
    SELECT option_name, CHAR_LENGTH(option_value) as value_length 
    FROM {$wpdb->options} 
    WHERE option_name LIKE 'redco_backup_%' 
       OR option_name LIKE 'redco_optimization_backup_%'
    ORDER BY option_name
");

echo "Backup options in database:\n";
if (!empty($backup_options)) {
    foreach ($backup_options as $option) {
        echo "  {$option->option_name} (size: {$option->value_length} chars)\n";
    }
} else {
    echo "  No backup options found in database\n";
}

// Check filesystem
$backup_dir = WP_CONTENT_DIR . '/redco-backups/';
echo "\nFilesystem backup directory: {$backup_dir}\n";

if (is_dir($backup_dir)) {
    echo "Directory exists. Contents:\n";
    $files = scandir($backup_dir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..') {
            $full_path = $backup_dir . $file;
            if (is_dir($full_path)) {
                echo "  [DIR] {$file}/\n";
            } else {
                echo "  [FILE] {$file} (" . filesize($full_path) . " bytes)\n";
            }
        }
    }
} else {
    echo "Directory does not exist\n";
}

echo "\n<h2>📋 Step 6: Summary and Recommendations</h2>\n";

// Provide specific recommendations based on findings
echo "Based on the analysis above, here are the likely issues:\n\n";

$issues_found = array();

// Check if migration is working
if (count($fix_history) === count($fix_history_after)) {
    $issues_found[] = "Migration function may not be detecting sessions that need rollback IDs";
}

// Check if backups exist
if (empty($backup_options) && !is_dir($backup_dir)) {
    $issues_found[] = "No backups found in any storage location - this explains unavailable rollback buttons";
}

// Check if sessions have proper structure
$sessions_without_rollback = 0;
foreach ($fix_history_after as $session) {
    if (isset($session['backup_created']) && $session['backup_created'] && 
        (!isset($session['rollback_id']) || empty($session['rollback_id']))) {
        $sessions_without_rollback++;
    }
}

if ($sessions_without_rollback > 0) {
    $issues_found[] = "{$sessions_without_rollback} sessions claim backup was created but have no rollback_id";
}

if (!empty($issues_found)) {
    echo "🚨 Issues Found:\n";
    foreach ($issues_found as $issue) {
        echo "  - {$issue}\n";
    }
} else {
    echo "✅ No obvious issues found. The problem may be more subtle.\n";
}

echo "\nNext steps for investigation:\n";
echo "1. Check if backup creation is actually working during fix application\n";
echo "2. Verify the backup storage mechanism is functioning correctly\n";
echo "3. Test end-to-end fix application and rollback workflow\n";
echo "4. Check for any errors in the WordPress error log\n";
