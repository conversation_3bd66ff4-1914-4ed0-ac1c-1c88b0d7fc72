<?php
/**
 * Enhanced Backup System for Redco Optimizer
 * 
 * Provides granular backup and rollback capabilities for Phase 1 enhancements
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Enhanced_Backup {
    
    private $backup_types = array(
        'full_site' => 'Complete site backup (files + database)',
        'files_only' => 'Files backup only',
        'database_only' => 'Database backup only',
        'selective' => 'Selective backup (specific files/tables)',
        'incremental' => 'Incremental backup (changes only)'
    );
    
    /**
     * Initialize the enhanced backup system
     */
    public function init() {
        // AJAX handlers for backup operations
        add_action('wp_ajax_redco_create_backup', array($this, 'ajax_create_backup'));
        add_action('wp_ajax_redco_restore_backup', array($this, 'ajax_restore_backup'));
        add_action('wp_ajax_redco_list_backups', array($this, 'ajax_list_backups'));
        add_action('wp_ajax_redco_delete_backup', array($this, 'ajax_delete_backup'));
        
        // Ensure backup directory exists
        $this->ensure_backup_directory();
    }
    
    /**
     * Create backup before applying fixes
     */
    public function create_pre_fix_backup($fix_details, $backup_config = array()) {
        $backup_id = wp_generate_uuid4();
        
        $default_config = array(
            'type' => $this->determine_backup_type($fix_details),
            'compression' => 'zip',
            'include_uploads' => false,
            'include_plugins' => true,
            'include_themes' => true,
            'include_database' => true,
            'retention_days' => 30,
            'verify_integrity' => true
        );
        
        $config = array_merge($default_config, $backup_config);
        
        try {
            $backup_info = array(
                'id' => $backup_id,
                'type' => $config['type'],
                'created_at' => current_time('mysql'),
                'created_by' => get_current_user_id(),
                'related_fix_id' => $fix_details['id'] ?? 'unknown',
                'config' => $config,
                'status' => 'creating',
                'files_included' => array(),
                'database_tables' => array(),
                'backup_size' => 0,
                'backup_hash' => '',
                'metadata' => array(
                    'wp_version' => get_bloginfo('version'),
                    'php_version' => PHP_VERSION,
                    'active_plugins' => get_option('active_plugins'),
                    'active_theme' => get_template()
                )
            );
            
            // Store initial backup info
            $this->store_backup_info($backup_info);
            
            // Create backup directory
            $backup_path = $this->create_backup_directory($backup_id);
            
            // Perform backup based on type
            $result = $this->perform_backup($backup_id, $backup_path, $config, $fix_details);
            
            if ($result['success']) {
                // Update backup info with results
                $backup_info['status'] = 'completed';
                $backup_info['backup_path'] = $backup_path;
                $backup_info['backup_size'] = $result['size'];
                $backup_info['backup_hash'] = $result['hash'];
                $backup_info['files_included'] = $result['files'];
                $backup_info['database_tables'] = $result['tables'] ?? array();
                
                $this->update_backup_info($backup_id, $backup_info);
                
                // Schedule cleanup
                $this->schedule_backup_cleanup($backup_id, $config['retention_days']);
                
                return array(
                    'success' => true,
                    'backup_id' => $backup_id,
                    'backup_info' => $backup_info
                );
                
            } else {
                // Mark backup as failed
                $backup_info['status'] = 'failed';
                $backup_info['error'] = $result['error'];
                $this->update_backup_info($backup_id, $backup_info);
                
                return array(
                    'success' => false,
                    'error' => $result['error']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => 'Backup creation failed: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Perform the actual backup operation
     */
    private function perform_backup($backup_id, $backup_path, $config, $fix_details) {
        switch ($config['type']) {
            case 'selective':
                return $this->create_selective_backup($backup_id, $backup_path, $config, $fix_details);
            case 'files_only':
                return $this->create_files_backup($backup_id, $backup_path, $config);
            case 'database_only':
                return $this->create_database_backup($backup_id, $backup_path, $config);
            case 'full_site':
            default:
                return $this->create_full_site_backup($backup_id, $backup_path, $config);
        }
    }
    
    /**
     * Create selective backup based on fix requirements
     */
    private function create_selective_backup($backup_id, $backup_path, $config, $fix_details) {
        $files_to_backup = array();
        $tables_to_backup = array();
        
        // Determine what to backup based on fix category
        $category = $fix_details['category'] ?? 'unknown';
        
        switch ($category) {
            case 'database':
                $tables_to_backup = $this->get_affected_database_tables($fix_details);
                break;
                
            case 'frontend':
                $files_to_backup = array_merge(
                    $this->get_theme_files(),
                    $this->get_css_js_files(),
                    array(ABSPATH . '.htaccess')
                );
                break;
                
            case 'server':
                $files_to_backup = array(
                    ABSPATH . '.htaccess',
                    ABSPATH . 'wp-config.php'
                );
                if (file_exists(ABSPATH . 'web.config')) {
                    $files_to_backup[] = ABSPATH . 'web.config';
                }
                break;
                
            case 'images':
                $files_to_backup = $this->get_image_files();
                break;
                
            default:
                // Backup critical files for unknown fix types
                $files_to_backup = array(
                    ABSPATH . '.htaccess',
                    ABSPATH . 'wp-config.php'
                );
                $tables_to_backup = array('options', 'postmeta', 'usermeta');
                break;
        }
        
        $backup_result = array(
            'success' => true,
            'files' => array(),
            'tables' => array(),
            'size' => 0,
            'hash' => ''
        );
        
        // Backup selected files
        if (!empty($files_to_backup)) {
            $file_backup_result = $this->backup_specific_files($files_to_backup, $backup_path);
            $backup_result['files'] = $file_backup_result['files'];
            $backup_result['size'] += $file_backup_result['size'];
        }
        
        // Backup selected database tables
        if (!empty($tables_to_backup)) {
            $db_backup_result = $this->backup_specific_tables($tables_to_backup, $backup_path);
            $backup_result['tables'] = $db_backup_result['tables'];
            $backup_result['size'] += $db_backup_result['size'];
        }
        
        // Generate backup hash
        $backup_result['hash'] = $this->generate_backup_hash($backup_path);
        
        return $backup_result;
    }
    
    /**
     * Backup specific files
     */
    private function backup_specific_files($files, $backup_path) {
        $backed_up_files = array();
        $total_size = 0;
        
        foreach ($files as $file) {
            if (file_exists($file) && is_readable($file)) {
                $relative_path = str_replace(ABSPATH, '', $file);
                $backup_file_path = $backup_path . '/files/' . $relative_path;
                
                // Create directory if needed
                $backup_dir = dirname($backup_file_path);
                if (!file_exists($backup_dir)) {
                    wp_mkdir_p($backup_dir);
                }
                
                // Copy file
                if (copy($file, $backup_file_path)) {
                    $backed_up_files[] = $relative_path;
                    $total_size += filesize($file);
                }
            }
        }
        
        return array(
            'files' => $backed_up_files,
            'size' => $total_size
        );
    }
    
    /**
     * Backup specific database tables
     */
    private function backup_specific_tables($tables, $backup_path) {
        global $wpdb;
        
        $backed_up_tables = array();
        $total_size = 0;
        
        $sql_file = $backup_path . '/database.sql';
        $handle = fopen($sql_file, 'w');
        
        if (!$handle) {
            return array('tables' => array(), 'size' => 0);
        }
        
        foreach ($tables as $table) {
            $full_table_name = $wpdb->prefix . $table;
            
            // Check if table exists
            $table_exists = $wpdb->get_var($wpdb->prepare(
                "SHOW TABLES LIKE %s",
                $full_table_name
            ));
            
            if ($table_exists) {
                // Get table structure
                $create_table = $wpdb->get_row("SHOW CREATE TABLE `{$full_table_name}`", ARRAY_N);
                if ($create_table) {
                    fwrite($handle, "DROP TABLE IF EXISTS `{$full_table_name}`;\n");
                    fwrite($handle, $create_table[1] . ";\n\n");
                }
                
                // Get table data
                $rows = $wpdb->get_results("SELECT * FROM `{$full_table_name}`", ARRAY_A);
                if ($rows) {
                    foreach ($rows as $row) {
                        $values = array();
                        foreach ($row as $value) {
                            $values[] = is_null($value) ? 'NULL' : "'" . esc_sql($value) . "'";
                        }
                        fwrite($handle, "INSERT INTO `{$full_table_name}` VALUES (" . implode(', ', $values) . ");\n");
                    }
                }
                
                $backed_up_tables[] = $table;
            }
        }
        
        fclose($handle);
        
        if (file_exists($sql_file)) {
            $total_size = filesize($sql_file);
        }
        
        return array(
            'tables' => $backed_up_tables,
            'size' => $total_size
        );
    }
    
    /**
     * Determine backup type based on fix details
     */
    private function determine_backup_type($fix_details) {
        $category = $fix_details['category'] ?? 'unknown';
        $tier = $fix_details['tier'] ?? 'safe';
        
        // Advanced tier fixes always get full backup
        if ($tier === 'advanced') {
            return 'full_site';
        }
        
        // Selective backup for specific categories
        if (in_array($category, array('database', 'frontend', 'server', 'images'))) {
            return 'selective';
        }
        
        // Default to selective for safety
        return 'selective';
    }
    
    /**
     * Get affected database tables based on fix details
     */
    private function get_affected_database_tables($fix_details) {
        $fix_id = $fix_details['id'] ?? '';
        
        // Map fix IDs to affected tables
        $table_mapping = array(
            'cleanup_transients' => array('options'),
            'optimize_autoload' => array('options'),
            'cleanup_revisions' => array('posts'),
            'cleanup_spam_comments' => array('comments', 'commentmeta'),
            'optimize_database_tables' => array('posts', 'postmeta', 'comments', 'commentmeta', 'options', 'users', 'usermeta')
        );
        
        return $table_mapping[$fix_id] ?? array('options');
    }
    
    /**
     * AJAX: Create backup
     */
    public function ajax_create_backup() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $backup_type = sanitize_text_field($_POST['backup_type'] ?? 'selective');
        $fix_details = array(
            'id' => sanitize_text_field($_POST['fix_id'] ?? 'manual_backup'),
            'category' => sanitize_text_field($_POST['category'] ?? 'unknown')
        );
        
        $result = $this->create_pre_fix_backup($fix_details, array('type' => $backup_type));
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result['error']);
        }
    }
    
    /**
     * UNIFIED: Ensure backup directory exists using centralized configuration
     */
    private function ensure_backup_directory() {
        // Ensure helpers file is loaded
        if (!function_exists('redco_get_unified_backup_dir')) {
            require_once(dirname(__FILE__) . '/../../../includes/helpers.php');
        }

        // Use unified backup directory function
        $backup_dir = redco_get_unified_backup_dir();
        // Directory creation and .htaccess protection is handled by the unified function
        error_log("REDCO UNIFIED: Using unified backup directory: {$backup_dir}");
    }
    
    /**
     * UNIFIED: Create backup directory for specific backup using centralized function
     */
    private function create_backup_directory($backup_id) {
        // Ensure helpers file is loaded
        if (!function_exists('redco_get_unified_backup_dir')) {
            require_once(dirname(__FILE__) . '/../../../includes/helpers.php');
        }

        $unified_backup_base = redco_get_unified_backup_dir();
        $backup_dir = $unified_backup_base . $backup_id;

        if (!file_exists($backup_dir)) {
            wp_mkdir_p($backup_dir);
            wp_mkdir_p($backup_dir . '/files');
        }

        error_log("REDCO UNIFIED: Created backup directory: {$backup_dir}");
        return $backup_dir;
    }
    
    /**
     * Store backup information in database
     */
    private function store_backup_info($backup_info) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_fix_history';
        
        return $wpdb->insert(
            $table_name,
            array(
                'fix_id' => 'backup_' . $backup_info['id'],
                'fix_type' => 'backup',
                'fix_tier' => 'safe',
                'fix_category' => 'backup',
                'executed_at' => $backup_info['created_at'],
                'executed_by' => $backup_info['created_by'],
                'execution_method' => 'manual',
                'success' => 1,
                'before_metrics' => json_encode($backup_info),
                'backup_id' => $backup_info['id']
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%d', '%s', '%s')
        );
    }
    
    /**
     * Update backup information
     */
    private function update_backup_info($backup_id, $backup_info) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_fix_history';
        
        return $wpdb->update(
            $table_name,
            array(
                'after_metrics' => json_encode($backup_info),
                'success' => $backup_info['status'] === 'completed' ? 1 : 0,
                'error_message' => $backup_info['error'] ?? null
            ),
            array('backup_id' => $backup_id),
            array('%s', '%d', '%s'),
            array('%s')
        );
    }

    /**
     * Helper methods for backup operations
     */
    private function get_theme_files() {
        $theme_dir = get_template_directory();
        $files = array();

        if (is_dir($theme_dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($theme_dir)
            );

            foreach ($iterator as $file) {
                if ($file->isFile() && in_array($file->getExtension(), array('php', 'css', 'js'))) {
                    $files[] = $file->getPathname();
                }
            }
        }

        return $files;
    }

    private function get_css_js_files() {
        $files = array();

        // Get theme CSS/JS files
        $theme_dir = get_template_directory();
        if (is_dir($theme_dir)) {
            $css_files = glob($theme_dir . '/*.css');
            $js_files = glob($theme_dir . '/*.js');
            $files = array_merge($files, $css_files, $js_files);
        }

        return $files;
    }

    private function get_image_files() {
        $upload_dir = wp_upload_dir();
        $files = array();

        if (is_dir($upload_dir['basedir'])) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($upload_dir['basedir'])
            );

            foreach ($iterator as $file) {
                if ($file->isFile() && in_array($file->getExtension(), array('jpg', 'jpeg', 'png', 'gif', 'webp'))) {
                    $files[] = $file->getPathname();
                }
            }
        }

        return $files;
    }

    private function generate_backup_hash($backup_path) {
        if (!file_exists($backup_path)) {
            return '';
        }

        $hash_data = '';

        // Hash all files in backup directory
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backup_path)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $hash_data .= md5_file($file->getPathname());
            }
        }

        return md5($hash_data);
    }

    private function schedule_backup_cleanup($backup_id, $retention_days) {
        $cleanup_time = time() + ($retention_days * 24 * 60 * 60);

        wp_schedule_single_event(
            $cleanup_time,
            'redco_cleanup_backup',
            array($backup_id)
        );
    }

    private function get_scheduled_fix($scheduled_fix_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %s", $scheduled_fix_id),
            ARRAY_A
        );
    }

    private function update_scheduled_fix_status($scheduled_fix_id, $status, $result = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        $update_data = array(
            'status' => $status
        );

        if ($status === 'completed' || $status === 'failed') {
            $update_data['executed_at'] = current_time('mysql');
            $update_data['execution_result'] = json_encode($result);
        }

        return $wpdb->update(
            $table_name,
            $update_data,
            array('id' => $scheduled_fix_id),
            array('%s', '%s', '%s'),
            array('%s')
        );
    }
}
