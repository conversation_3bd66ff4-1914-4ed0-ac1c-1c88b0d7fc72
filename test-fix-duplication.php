<?php
/**
 * Test script to verify that the Recent Fixes duplication issue is resolved
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== TESTING FIX DUPLICATION ISSUE ===\n";

// Clear existing fix history for clean test
delete_option('redco_diagnostic_fix_history');
echo "Cleared existing fix history for clean test\n";

// Create a test issue to fix
$test_issue = array(
    'id' => 'test_compression',
    'title' => 'Test Compression Issue',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true,
    'description' => 'Test issue for duplication testing'
);

echo "\nTest issue created: {$test_issue['id']}\n";

// Test 1: Apply fix using engine directly
echo "\n--- TEST 1: Engine Direct Application ---\n";
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
$engine = new Redco_Diagnostic_AutoFix_Engine();

$engine_result = $engine->apply_fix($test_issue);
echo "Engine result success: " . ($engine_result['success'] ? 'YES' : 'NO') . "\n";
echo "Engine rollback ID: " . ($engine_result['rollback_id'] ?? 'NONE') . "\n";

// Check fix history after engine application
$fix_history_after_engine = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after engine: " . count($fix_history_after_engine) . "\n";

if (!empty($fix_history_after_engine)) {
    $last_session = end($fix_history_after_engine);
    echo "Last session fixes applied: " . $last_session['fixes_applied'] . "\n";
    echo "Last session rollback ID: " . ($last_session['rollback_id'] ?? 'NONE') . "\n";
}

// Test 2: Simulate the AJAX single fix application (without duplicate recording)
echo "\n--- TEST 2: AJAX Single Fix Simulation ---\n";

// Clear history again for clean test
delete_option('redco_diagnostic_fix_history');

// Create another test issue
$test_issue_2 = array(
    'id' => 'test_compression_2',
    'title' => 'Test Compression Issue 2',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true,
    'description' => 'Second test issue for duplication testing'
);

// Simulate the AJAX flow (without the duplicate recording we removed)
$engine_2 = new Redco_Diagnostic_AutoFix_Engine();
$fix_result = $engine_2->apply_fix($test_issue_2);

echo "AJAX simulation result success: " . ($fix_result['success'] ? 'YES' : 'NO') . "\n";
echo "AJAX simulation rollback ID: " . ($fix_result['rollback_id'] ?? 'NONE') . "\n";

// Check fix history after AJAX simulation
$fix_history_after_ajax = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after AJAX simulation: " . count($fix_history_after_ajax) . "\n";

if (!empty($fix_history_after_ajax)) {
    $last_session = end($fix_history_after_ajax);
    echo "Last session fixes applied: " . $last_session['fixes_applied'] . "\n";
    echo "Last session rollback ID: " . ($last_session['rollback_id'] ?? 'NONE') . "\n";
}

// Test 3: Test Recent Fixes display
echo "\n--- TEST 3: Recent Fixes Display ---\n";

require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
$diagnostic = new Redco_Diagnostic_AutoFix();

// Use reflection to access the AJAX method
$reflection = new ReflectionClass($diagnostic);
$ajax_method = $reflection->getMethod('ajax_load_recent_fixes');
$ajax_method->setAccessible(true);

// Simulate AJAX request data
$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

// Capture output
ob_start();
try {
    $ajax_method->invoke($diagnostic);
} catch (Exception $e) {
    echo "AJAX method exception: " . $e->getMessage() . "\n";
}
$ajax_output = ob_get_clean();

// Parse the JSON response
if (!empty($ajax_output)) {
    $response_data = json_decode($ajax_output, true);
    if ($response_data && isset($response_data['success']) && $response_data['success']) {
        $html = $response_data['data']['html'] ?? '';
        
        // Count fix-item divs in the HTML
        $fix_item_count = substr_count($html, 'class="fix-item"');
        echo "Recent Fixes HTML contains $fix_item_count fix-item divs\n";
        
        // Check for duplicate content
        if ($fix_item_count > 1) {
            echo "⚠️  Multiple fix items found - checking for duplicates...\n";
            
            // Extract rollback IDs from HTML
            preg_match_all('/data-backup-id="([^"]+)"/', $html, $matches);
            $rollback_ids = $matches[1] ?? array();
            
            echo "Rollback IDs found in HTML: " . implode(', ', $rollback_ids) . "\n";
            
            $unique_ids = array_unique($rollback_ids);
            if (count($rollback_ids) !== count($unique_ids)) {
                echo "❌ DUPLICATE ROLLBACK IDS DETECTED!\n";
            } else {
                echo "✅ No duplicate rollback IDs found\n";
            }
        } else {
            echo "✅ Single fix item found (expected for one fix)\n";
        }
        
        // Show sample of HTML for debugging
        if (strlen($html) > 0) {
            echo "\nSample HTML (first 200 chars):\n";
            echo substr($html, 0, 200) . "...\n";
        }
    } else {
        echo "AJAX response failed or invalid\n";
        echo "Raw output: " . substr($ajax_output, 0, 200) . "\n";
    }
} else {
    echo "No AJAX output received\n";
}

// Final verification
echo "\n--- FINAL VERIFICATION ---\n";
$final_fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Final fix history count: " . count($final_fix_history) . "\n";

if (count($final_fix_history) === 1) {
    echo "✅ DUPLICATION ISSUE RESOLVED!\n";
    echo "Only one fix session recorded for one fix applied.\n";
} else {
    echo "❌ DUPLICATION ISSUE PERSISTS!\n";
    echo "Expected 1 fix session, found " . count($final_fix_history) . "\n";
    
    // Show details of all sessions
    foreach ($final_fix_history as $index => $session) {
        echo "Session $index: fixes_applied={$session['fixes_applied']}, rollback_id=" . ($session['rollback_id'] ?? 'NONE') . "\n";
    }
}

echo "\n=== TEST COMPLETE ===\n";
