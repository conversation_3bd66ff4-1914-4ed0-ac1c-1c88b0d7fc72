<?php
/**
 * Debug script to investigate backup creation and session recording issues
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

echo "=== DEBUGGING BACKUP AND SESSION ISSUES ===\n";

// Check current state
echo "\n1. CURRENT STATE CHECK:\n";
$current_history = get_option('redco_diagnostic_fix_history', array());
echo "Current fix history count: " . count($current_history) . "\n";

if (!empty($current_history)) {
    echo "Recent sessions:\n";
    foreach (array_slice($current_history, -3) as $index => $session) {
        echo "  Session: fixes_applied={$session['fixes_applied']}, rollback_id=" . ($session['rollback_id'] ?? 'NONE') . "\n";
    }
} else {
    echo "No fix history found\n";
}

// Check backup directory
echo "\n2. BACKUP DIRECTORY CHECK:\n";
$engine = new Redco_Diagnostic_AutoFix_Engine();

// Use reflection to access private properties and methods
$reflection = new ReflectionClass($engine);
$backup_dir_property = $reflection->getProperty('backup_dir');
$backup_dir_property->setAccessible(true);
$backup_dir = $backup_dir_property->getValue($engine);

echo "Engine backup directory: $backup_dir\n";
echo "Directory exists: " . (is_dir($backup_dir) ? 'YES' : 'NO') . "\n";
echo "Directory writable: " . (is_writable($backup_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($backup_dir)) {
    $existing_backups = glob($backup_dir . 'backup_*');
    echo "Existing backup directories: " . count($existing_backups) . "\n";
    
    if (!empty($existing_backups)) {
        echo "Recent backups:\n";
        foreach (array_slice($existing_backups, -3) as $backup) {
            echo "  - " . basename($backup) . "\n";
        }
    }
}

// Test backup creation directly
echo "\n3. TESTING BACKUP CREATION:\n";
$create_backup_method = $reflection->getMethod('create_backup');
$create_backup_method->setAccessible(true);

echo "Attempting to create backup...\n";
$backup_id = $create_backup_method->invoke($engine);

if ($backup_id) {
    echo "Backup ID generated: $backup_id\n";
    
    $backup_path = $backup_dir . $backup_id;
    echo "Expected backup path: $backup_path\n";
    echo "Backup directory created: " . (is_dir($backup_path) ? 'YES' : 'NO') . "\n";
    
    if (is_dir($backup_path)) {
        $backup_files = glob($backup_path . '/*');
        echo "Files in backup: " . count($backup_files) . "\n";
        foreach ($backup_files as $file) {
            echo "  - " . basename($file) . "\n";
        }
    }
} else {
    echo "❌ Backup creation failed - no backup ID returned\n";
}

// Test fix application with detailed logging
echo "\n4. TESTING FIX APPLICATION:\n";
$test_issue = array(
    'id' => 'debug_test',
    'title' => 'Debug Test Issue',
    'fix_action' => 'enable_compression',
    'auto_fixable' => true
);

echo "Applying test fix...\n";
$fix_result = $engine->apply_fix($test_issue);

echo "Fix success: " . ($fix_result['success'] ? 'YES' : 'NO') . "\n";
echo "Fix message: " . ($fix_result['message'] ?? 'No message') . "\n";
echo "Rollback ID: " . ($fix_result['rollback_id'] ?? 'NONE') . "\n";

if (isset($fix_result['rollback_id']) && !empty($fix_result['rollback_id'])) {
    $rollback_path = $backup_dir . $fix_result['rollback_id'];
    echo "Rollback directory exists: " . (is_dir($rollback_path) ? 'YES' : 'NO') . "\n";
}

// Check session recording after fix
echo "\n5. SESSION RECORDING CHECK:\n";
$updated_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after test: " . count($updated_history) . "\n";

if (count($updated_history) > count($current_history)) {
    $new_session = end($updated_history);
    echo "New session recorded:\n";
    echo "  - Timestamp: " . date('Y-m-d H:i:s', $new_session['timestamp']) . "\n";
    echo "  - Fixes applied: " . $new_session['fixes_applied'] . "\n";
    echo "  - Rollback ID: " . ($new_session['rollback_id'] ?? 'NONE') . "\n";
    echo "  - Details count: " . count($new_session['details'] ?? array()) . "\n";
} else {
    echo "❌ No new session recorded\n";
}

// Test Recent Fixes AJAX simulation
echo "\n6. RECENT FIXES AJAX TEST:\n";
try {
    require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
    
    // Create user context for AJAX test
    wp_set_current_user(1); // Assume admin user ID 1 exists
    
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    
    // Capture output
    ob_start();
    
    // Use reflection to call the AJAX method
    $ajax_reflection = new ReflectionClass($diagnostic);
    $ajax_method = $ajax_reflection->getMethod('ajax_load_recent_fixes');
    $ajax_method->setAccessible(true);
    
    try {
        $ajax_method->invoke($diagnostic);
        $ajax_output = ob_get_clean();
        
        echo "AJAX output received: " . (strlen($ajax_output) > 0 ? 'YES' : 'NO') . "\n";
        
        if (strlen($ajax_output) > 0) {
            // Try to parse JSON response
            $response = json_decode($ajax_output, true);
            if ($response) {
                echo "AJAX response success: " . ($response['success'] ? 'YES' : 'NO') . "\n";
                
                if ($response['success'] && isset($response['data']['html'])) {
                    $html = $response['data']['html'];
                    $fix_items = substr_count($html, 'class="fix-item"');
                    echo "Fix items in HTML: $fix_items\n";
                    
                    if ($fix_items > 0) {
                        echo "✅ Recent Fixes HTML generated successfully\n";
                        echo "Sample HTML (first 200 chars):\n" . substr($html, 0, 200) . "...\n";
                    } else {
                        echo "❌ No fix items in HTML\n";
                        echo "HTML content: " . substr($html, 0, 500) . "\n";
                    }
                } else {
                    echo "❌ AJAX response failed or no HTML data\n";
                    echo "Response: " . substr($ajax_output, 0, 500) . "\n";
                }
            } else {
                echo "❌ Invalid JSON response\n";
                echo "Raw output: " . substr($ajax_output, 0, 500) . "\n";
            }
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ AJAX method exception: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ AJAX test setup failed: " . $e->getMessage() . "\n";
}

// Summary
echo "\n=== SUMMARY ===\n";
echo "Issues found:\n";

// Check backup creation
$backup_issue = !$backup_id || !is_dir($backup_dir . $backup_id);
if ($backup_issue) {
    echo "❌ Backup creation not working properly\n";
} else {
    echo "✅ Backup creation working\n";
}

// Check session recording
$session_issue = count($updated_history) <= count($current_history);
if ($session_issue) {
    echo "❌ Session recording not working properly\n";
} else {
    echo "✅ Session recording working\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
