<?php
/**
 * Memory-Efficient Rollback Test
 * Designed to avoid PHP memory exhaustion during rollback testing
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

// MEMORY MONITORING
$initial_memory = memory_get_usage();
$memory_limit = ini_get('memory_limit');

echo "<h1>🧠 Memory-Efficient Rollback Test</h1>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>⚡ Memory Status</h3>\n";
echo "<ul>\n";
echo "<li><strong>Initial Memory:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
echo "<li><strong>Memory Limit:</strong> {$memory_limit}</li>\n";
echo "<li><strong>Available:</strong> " . round((ini_get('memory_limit') ? (int)$memory_limit * 1024 * 1024 - $initial_memory : 0) / 1024 / 1024, 2) . " MB</li>\n";
echo "</ul>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_07-41-34_68429bae30c91';

// Get current fix history (memory efficient)
$fix_history = get_option('redco_diagnostic_fix_history', array());
$session_count = count($fix_history);

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check if backup ID exists (memory efficient)
$backup_found = false;
$matching_session_index = -1;

foreach ($fix_history as $index => $session) {
    $session_rollback_id = $session['rollback_id'] ?? '';
    $session_backup_id = $session['backup_id'] ?? '';
    
    if ($session_rollback_id === $specific_backup_id || $session_backup_id === $specific_backup_id) {
        $backup_found = true;
        $matching_session_index = $index;
        break;
    }
}

if (!$backup_found) {
    echo "<h2>🔧 Creating Minimal Test Session</h2>\n";
    
    // Create minimal test session to avoid memory issues
    $test_session = array(
        'session_id' => 'memory_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Memory-efficient test session',
        'fixes_applied' => 1,
        'backup_created' => true,
        'details' => array(
            array(
                'issue_id' => 'memory_test_issue',
                'success' => true,
                'rollback_id' => $specific_backup_id
            )
        )
    );
    
    $fix_history[] = $test_session;
    $updated = update_option('redco_diagnostic_fix_history', $fix_history);
    
    if ($updated) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<p>✅ Minimal test session created successfully</p>\n";
        echo "</div>\n";
        $backup_found = true;
        $session_count = count($fix_history);
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<p>❌ Failed to create test session</p>\n";
        echo "</div>\n";
        exit;
    }
}

// Memory check before rollback
$pre_rollback_memory = memory_get_usage();
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🧠 Pre-Rollback Memory Status</h3>\n";
echo "<p><strong>Memory Usage:</strong> " . round($pre_rollback_memory / 1024 / 1024, 2) . " MB</p>\n";
echo "</div>\n";

// Execute memory-efficient rollback test
if (isset($_GET['execute_rollback'])) {
    echo "<h2>🧪 Executing Memory-Efficient Rollback</h2>\n";
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up minimal POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>📡 Executing AJAX Rollback</h3>\n";
        echo "<p><strong>Backup ID:</strong> {$specific_backup_id}</p>\n";
        echo "<p><strong>Sessions Before:</strong> {$session_count}</p>\n";
        echo "</div>\n";
        
        // Execute rollback with output buffering
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        // Memory check after rollback
        $post_rollback_memory = memory_get_usage();
        $memory_used = $post_rollback_memory - $pre_rollback_memory;
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🧠 Post-Rollback Memory Status</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Memory After:</strong> " . round($post_rollback_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Memory Used:</strong> " . round($memory_used / 1024 / 1024, 2) . " MB</li>\n";
        echo "<li><strong>Output Length:</strong> " . strlen($output) . " characters</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Parse response
        $response = json_decode($output, true);
        
        if ($response && isset($response['success'])) {
            if ($response['success']) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>✅ Rollback Successful</h3>\n";
                echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
                echo "</div>\n";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>❌ Rollback Failed</h3>\n";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
                echo "</div>\n";
            }
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
            echo "<h3>❌ Invalid Response</h3>\n";
            echo "<p>Response was not valid JSON or had unexpected format.</p>\n";
            if (strlen($output) < 500) {
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>" . htmlspecialchars($output) . "</pre>\n";
            }
            echo "</div>\n";
        }
        
        // Check database state
        $updated_fix_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($updated_fix_history);
        $sessions_removed = $session_count - $final_count;
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>📊 Database Results</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$sessions_removed}</li>\n";
        echo "</ul>\n";
        
        if ($sessions_removed > 0) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>✅ SUCCESS:</strong> {$sessions_removed} session(s) removed from Recent Fixes!</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ ISSUE:</strong> No sessions were removed from the database.</p>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Rollback</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to verify the Recent Fixes list</li>\n";
    echo "<li>Check if the rolled-back item has been removed from the UI</li>\n";
    echo "<li>Refresh the page to ensure the change persists</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>🧪 Ready for Memory-Efficient Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 Test Instructions</h3>\n";
    echo "<p>This test is designed to avoid memory exhaustion issues while testing the rollback functionality.</p>\n";
    echo "<p><a href='?execute_rollback=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Execute Memory-Efficient Rollback</a></p>\n";
    echo "</div>\n";
}

// Final memory status
$final_memory = memory_get_usage();
echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-top: 20px;'>\n";
echo "<h3>🧠 Final Memory Status</h3>\n";
echo "<ul>\n";
echo "<li><strong>Initial Memory:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
echo "<li><strong>Final Memory:</strong> " . round($final_memory / 1024 / 1024, 2) . " MB</li>\n";
echo "<li><strong>Total Used:</strong> " . round(($final_memory - $initial_memory) / 1024 / 1024, 2) . " MB</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>
