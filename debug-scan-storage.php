<?php
// Debug script to check scan storage and issue detection
echo "Starting debug script...\n";

try {
    require_once('D:/xampp/htdocs/wordpress/wp-config.php');
    echo "WordPress loaded successfully\n";
} catch (Exception $e) {
    echo "Failed to load WordPress: " . $e->getMessage() . "\n";
    exit(1);
}

echo "=== SCAN STORAGE & ISSUE DETECTION DEBUG ===\n";

// Check if main module class exists
if (class_exists('Redco_Diagnostic_AutoFix')) {
    echo "✅ Main module class exists\n";
    
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Test scan execution
    echo "\n🔍 TESTING SCAN EXECUTION:\n";
    try {
        $results = $diagnostic->run_diagnostic_scan('comprehensive', false);
        
        echo "Scan completed successfully!\n";
        echo "- Scan type: " . ($results['scan_type'] ?? 'unknown') . "\n";
        echo "- Issues found: " . count($results['issues'] ?? array()) . "\n";
        echo "- Performance score: " . ($results['performance_score'] ?? 'N/A') . "\n";
        echo "- Health score: " . ($results['health_score'] ?? 'N/A') . "\n";
        
        if (!empty($results['issues'])) {
            echo "\nFirst 3 issues detected:\n";
            foreach (array_slice($results['issues'], 0, 3) as $i => $issue) {
                echo "  " . ($i+1) . ". " . ($issue['title'] ?? 'No title') . "\n";
                echo "     Severity: " . ($issue['severity'] ?? 'unknown') . "\n";
                echo "     Auto-fixable: " . (($issue['auto_fixable'] ?? false) ? 'Yes' : 'No') . "\n";
            }
        } else {
            echo "❌ NO ISSUES DETECTED - This might be the problem!\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Scan failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Main module class not found\n";
}

// Check database table after scan
echo "\n📊 DATABASE CHECK AFTER SCAN:\n";
global $wpdb;
$table_name = $wpdb->prefix . 'redco_diagnostic_results';

$scan_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
echo "Total scans in database: {$scan_count}\n";

if ($scan_count > 0) {
    $latest_scan = $wpdb->get_row(
        "SELECT id, timestamp, issues_found, scan_data FROM {$table_name} ORDER BY timestamp DESC LIMIT 1"
    );
    
    echo "Latest scan:\n";
    echo "- ID: {$latest_scan->id}\n";
    echo "- Timestamp: {$latest_scan->timestamp}\n";
    echo "- Issues found: {$latest_scan->issues_found}\n";
    
    if ($latest_scan->scan_data) {
        $scan_data = json_decode($latest_scan->scan_data, true);
        echo "- Actual issues in data: " . count($scan_data['issues'] ?? array()) . "\n";
    }
}

// Check WordPress options storage
echo "\n📝 WORDPRESS OPTIONS CHECK:\n";
$option_results = get_option('redco_diagnostic_results', array());
if (!empty($option_results)) {
    echo "WordPress option exists\n";
    echo "- Issues in option: " . count($option_results['issues'] ?? array()) . "\n";
} else {
    echo "❌ No WordPress option data\n";
}

echo "\n=== END DEBUG ===\n";
?>
