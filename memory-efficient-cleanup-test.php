<?php
/**
 * Memory-Efficient Cleanup Test
 * Tests the new rollback approach with comprehensive cleanup
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧹 Memory-Efficient Cleanup Test</h1>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🎯 Complete Rollback Solution</h3>\n";
echo "<p>This test verifies the new memory-efficient rollback that:</p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Updates Recent Fixes list</strong> (removes from database)</li>\n";
echo "<li>✅ <strong>Removes backup folder</strong> from redco-backups directory</li>\n";
echo "<li>✅ <strong>Removes backup .htaccess</strong> from document root</li>\n";
echo "<li>✅ <strong>Updates stats</strong> efficiently without heavy recalculation</li>\n";
echo "<li>✅ <strong>Uses minimal memory</strong> (under 10MB)</li>\n";
echo "<li>⚠️ <strong>Skips file restoration</strong> (to prevent memory exhaustion)</li>\n";
echo "</ul>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_08-52-41_6842ac593eed8';

// Check current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$current_stats = get_option('redco_diagnostic_stats', array());
$session_count = count($fix_history);

echo "<h2>📊 Pre-Rollback State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "<li><strong>Current Fixes Applied (Stats):</strong> " . ($current_stats['fixes_applied'] ?? 0) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check if cleanup targets exist
$backup_dir = WP_CONTENT_DIR . '/uploads/redco-backups/' . $specific_backup_id;
$backup_htaccess = ABSPATH . '.htaccess.backup.' . $specific_backup_id;

echo "<h2>🔍 Cleanup Targets Analysis</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>📁 Files/Folders to Clean</h3>\n";
echo "<ul>\n";
echo "<li><strong>Backup Directory:</strong> " . (is_dir($backup_dir) ? '✅ EXISTS' : '❌ NOT FOUND') . "<br><code>{$backup_dir}</code></li>\n";
echo "<li><strong>Backup .htaccess:</strong> " . (file_exists($backup_htaccess) ? '✅ EXISTS' : '❌ NOT FOUND') . "<br><code>{$backup_htaccess}</code></li>\n";
echo "</ul>\n";

// Create test files if they don't exist
$created_test_files = false;
if (!is_dir($backup_dir)) {
    wp_mkdir_p($backup_dir);
    file_put_contents($backup_dir . '/test_file.txt', 'Test backup content');
    $created_test_files = true;
    echo "<p>📝 <strong>Created test backup directory for testing</strong></p>\n";
}

if (!file_exists($backup_htaccess)) {
    file_put_contents($backup_htaccess, '# Test backup .htaccess file');
    $created_test_files = true;
    echo "<p>📝 <strong>Created test backup .htaccess for testing</strong></p>\n";
}

if ($created_test_files) {
    echo "<p><em>Test files created to demonstrate cleanup functionality.</em></p>\n";
}
echo "</div>\n";

// Check if target session exists
$backup_found = false;
$matching_session = null;

foreach ($fix_history as $session) {
    if (($session['rollback_id'] ?? '') === $specific_backup_id || 
        ($session['backup_id'] ?? '') === $specific_backup_id) {
        $backup_found = true;
        $matching_session = $session;
        break;
    }
}

if (!$backup_found) {
    echo "<h2>🔧 Creating Test Session</h2>\n";
    
    $test_session = array(
        'session_id' => 'cleanup_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Memory-efficient cleanup test session',
        'fixes_applied' => 1,
        'backup_created' => true
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    // Update stats to reflect the added fix
    $current_stats['fixes_applied'] = ($current_stats['fixes_applied'] ?? 0) + 1;
    update_option('redco_diagnostic_stats', $current_stats);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>✅ Created test session and updated stats for comprehensive testing</p>\n";
    echo "</div>\n";
    
    $session_count = count($fix_history);
}

// Execute memory-efficient cleanup test
if (isset($_GET['execute'])) {
    echo "<h2>🧹 Executing Memory-Efficient Cleanup Rollback</h2>\n";
    
    $initial_memory = memory_get_usage();
    $pre_stats = get_option('redco_diagnostic_stats', array());
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Comprehensive Rollback</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Stats Fixes Before:</strong> " . ($pre_stats['fixes_applied'] ?? 0) . "</li>\n";
        echo "<li><strong>Memory Before:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Execute rollback
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        $final_memory = memory_get_usage();
        $memory_used = $final_memory - $initial_memory;
        
        // Parse response
        $response = json_decode($output, true);
        $is_valid_json = ($response !== null);
        
        echo "<div style='background: " . ($is_valid_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_valid_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_valid_json ? '✅' : '❌') . " AJAX Response</h3>\n";
        
        if ($is_valid_json && ($response['success'] ?? false)) {
            echo "<p><strong>✅ SUCCESS:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
            
            if (isset($response['data']['cleanup_performed']['details'])) {
                $cleanup = $response['data']['cleanup_performed']['details'];
                echo "<h4>🧹 Cleanup Results:</h4>\n";
                echo "<ul>\n";
                if (!empty($cleanup['completed'])) {
                    foreach ($cleanup['completed'] as $task) {
                        echo "<li>✅ {$task}</li>\n";
                    }
                }
                if (!empty($cleanup['failed'])) {
                    foreach ($cleanup['failed'] as $task) {
                        echo "<li>❌ {$task}</li>\n";
                    }
                }
                echo "</ul>\n";
                
                if (!empty($cleanup['errors'])) {
                    echo "<h4>⚠️ Cleanup Errors:</h4>\n";
                    echo "<ul>\n";
                    foreach ($cleanup['errors'] as $error) {
                        echo "<li>" . htmlspecialchars($error) . "</li>\n";
                    }
                    echo "</ul>\n";
                }
            }
        } else {
            echo "<p><strong>❌ FAILED:</strong> " . htmlspecialchars($response['data'] ?? $output) . "</p>\n";
        }
        echo "</div>\n";
        
        // Verify cleanup results
        echo "<h3>🔍 Cleanup Verification</h3>\n";
        
        $post_backup_dir_exists = is_dir($backup_dir);
        $post_backup_htaccess_exists = file_exists($backup_htaccess);
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $post_stats = get_option('redco_diagnostic_stats', array());
        
        $final_count = count($final_history);
        $sessions_removed = $session_count - $final_count;
        $stats_fixes_after = $post_stats['fixes_applied'] ?? 0;
        $stats_updated = ($pre_stats['fixes_applied'] ?? 0) !== $stats_fixes_after;
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📊 Verification Results</h4>\n";
        echo "<table style='width: 100%; border-collapse: collapse;'>\n";
        echo "<tr><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Task</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Before</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>After</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Status</th></tr>\n";
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Recent Fixes Sessions</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$session_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$final_count}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($sessions_removed > 0 ? '✅ Removed' : '❌ Not removed') . "</td></tr>\n";
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Backup Directory</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>EXISTS</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_backup_dir_exists ? 'EXISTS' : 'REMOVED') . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . (!$post_backup_dir_exists ? '✅ Cleaned' : '❌ Still exists') . "</td></tr>\n";
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Backup .htaccess</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>EXISTS</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_backup_htaccess_exists ? 'EXISTS' : 'REMOVED') . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . (!$post_backup_htaccess_exists ? '✅ Cleaned' : '❌ Still exists') . "</td></tr>\n";
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Stats Fixes Count</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($pre_stats['fixes_applied'] ?? 0) . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$stats_fixes_after}</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($stats_updated ? '✅ Updated' : '❌ Not updated') . "</td></tr>\n";
        echo "<tr><td style='padding: 8px;'>Memory Usage</td><td style='padding: 8px;'>" . round($initial_memory / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>" . round($final_memory / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>" . ($memory_used < 10 * 1024 * 1024 ? '✅ Efficient' : '⚠️ High') . " (+" . round($memory_used / 1024 / 1024, 2) . " MB)</td></tr>\n";
        echo "</table>\n";
        echo "</div>\n";
        
        // Overall success analysis
        $all_cleaned = !$post_backup_dir_exists && !$post_backup_htaccess_exists;
        $database_updated = $sessions_removed > 0;
        $memory_efficient = $memory_used < 10 * 1024 * 1024;
        $overall_success = $all_cleaned && $database_updated && $stats_updated && $memory_efficient;
        
        echo "<div style='background: " . ($overall_success ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($overall_success ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>" . ($overall_success ? '🎉' : '⚠️') . " Overall Test Result</h3>\n";
        
        if ($overall_success) {
            echo "<p><strong>🎉 COMPLETE SUCCESS!</strong></p>\n";
            echo "<p>All identified problems have been resolved:</p>\n";
            echo "<ul>\n";
            echo "<li>✅ Recent Fixes list properly updated</li>\n";
            echo "<li>✅ Backup folder removed from redco-backups</li>\n";
            echo "<li>✅ Backup .htaccess removed from document root</li>\n";
            echo "<li>✅ Stats updated efficiently</li>\n";
            echo "<li>✅ Memory usage under control (" . round($memory_used / 1024 / 1024, 2) . " MB)</li>\n";
            echo "</ul>\n";
        } else {
            echo "<p><strong>⚠️ Some Issues Remain</strong></p>\n";
            echo "<ul>\n";
            echo "<li>" . ($database_updated ? '✅' : '❌') . " Recent Fixes list updated</li>\n";
            echo "<li>" . (!$post_backup_dir_exists ? '✅' : '❌') . " Backup folder removed</li>\n";
            echo "<li>" . (!$post_backup_htaccess_exists ? '✅' : '❌') . " Backup .htaccess removed</li>\n";
            echo "<li>" . ($stats_updated ? '✅' : '❌') . " Stats updated</li>\n";
            echo "<li>" . ($memory_efficient ? '✅' : '❌') . " Memory efficient</li>\n";
            echo "</ul>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Verify that the Recent Fixes list has been updated</li>\n";
    echo "<li>Check that no memory errors occurred in the error logs</li>\n";
    echo "<li>Confirm that backup files have been cleaned up</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>🧹 Ready for Comprehensive Cleanup Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 What This Test Will Do</h3>\n";
    echo "<p>This comprehensive test will verify that the memory-efficient rollback:</p>\n";
    echo "<ol>\n";
    echo "<li>✅ <strong>Updates Recent Fixes</strong> - Removes session from database</li>\n";
    echo "<li>✅ <strong>Cleans backup folder</strong> - Removes from redco-backups directory</li>\n";
    echo "<li>✅ <strong>Cleans backup .htaccess</strong> - Removes from document root</li>\n";
    echo "<li>✅ <strong>Updates stats</strong> - Decrements fixes count efficiently</li>\n";
    echo "<li>✅ <strong>Uses minimal memory</strong> - Under 10MB usage</li>\n";
    echo "<li>✅ <strong>Completes without errors</strong> - No memory exhaustion</li>\n";
    echo "</ol>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧹 Execute Comprehensive Cleanup Test</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='database-only-rollback-test.php'>💾 Database-Only Test</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
