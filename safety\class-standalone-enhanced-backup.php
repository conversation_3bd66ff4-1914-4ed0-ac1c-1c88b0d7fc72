<?php
/**
 * Standalone Enhanced Backup System for Redco Diagnostic & Auto-Fix
 * 
 * Phase 1 Enhancement: Advanced backup and rollback functionality (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Enhanced_Backup {
    
    private $backup_dir;
    private $max_backups = 10;
    
    /**
     * UNIFIED: Constructor using centralized backup directory
     */
    public function __construct() {
        $this->backup_dir = redco_get_unified_backup_dir();
        error_log("REDCO UNIFIED: Standalone backup using unified directory: {$this->backup_dir}");
    }
    
    /**
     * Initialize the enhanced backup system
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_create_backup', array($this, 'ajax_create_backup'));
        add_action('wp_ajax_redco_diagnostic_restore_backup', array($this, 'ajax_restore_backup'));
        add_action('wp_ajax_redco_diagnostic_delete_backup', array($this, 'ajax_delete_backup'));
        add_action('wp_ajax_redco_diagnostic_list_backups', array($this, 'ajax_list_backups'));
        
        // Hooks for automatic backup creation
        add_action('redco_diagnostic_before_fix_apply', array($this, 'create_automatic_backup'), 10, 2);
        
        // Cleanup old backups
        add_action('redco_diagnostic_cleanup_backups', array($this, 'cleanup_old_backups'));
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('redco_diagnostic_cleanup_backups')) {
            wp_schedule_event(time(), 'daily', 'redco_diagnostic_cleanup_backups');
        }
        
        // Create database table for backup metadata
        $this->maybe_create_backup_table();
    }
    
    /**
     * AJAX: Create backup
     */
    public function ajax_create_backup() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $backup_type = sanitize_text_field($_POST['backup_type'] ?? 'manual');
        $description = sanitize_text_field($_POST['description'] ?? '');
        
        try {
            $backup_id = $this->create_backup($backup_type, $description);
            
            wp_send_json_success(array(
                'backup_id' => $backup_id,
                'message' => 'Backup created successfully',
                'backup_info' => $this->get_backup_info($backup_id)
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Backup failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Create backup
     */
    public function create_backup($type = 'manual', $description = '', $fix_context = array()) {
        $backup_id = 'backup_' . time() . '_' . wp_generate_password(8, false);
        $backup_path = $this->backup_dir . $backup_id . '/';
        
        // Create backup directory
        if (!wp_mkdir_p($backup_path)) {
            throw new Exception('Failed to create backup directory');
        }
        
        redco_diagnostic_log("Creating backup (ID: {$backup_id}, Type: {$type})");
        
        $backup_data = array(
            'backup_id' => $backup_id,
            'type' => $type,
            'description' => $description,
            'created_at' => current_time('mysql'),
            'created_by' => get_current_user_id(),
            'fix_context' => $fix_context,
            'files' => array(),
            'database_changes' => array(),
            'settings_backup' => array(),
            'size' => 0
        );
        
        // Backup critical files
        $backup_data['files'] = $this->backup_critical_files($backup_path);
        
        // Backup database settings
        $backup_data['settings_backup'] = $this->backup_settings();
        
        // Backup .htaccess if it exists
        if (file_exists(ABSPATH . '.htaccess')) {
            $htaccess_backup = $backup_path . 'htaccess_backup.txt';
            copy(ABSPATH . '.htaccess', $htaccess_backup);
            $backup_data['files']['htaccess'] = $htaccess_backup;
        }
        
        // Calculate backup size
        $backup_data['size'] = $this->calculate_backup_size($backup_path);
        
        // Save backup metadata
        $this->save_backup_metadata($backup_data);
        
        // Save backup manifest
        file_put_contents(
            $backup_path . 'manifest.json',
            json_encode($backup_data, JSON_PRETTY_PRINT)
        );
        
        redco_diagnostic_log("Backup created successfully (ID: {$backup_id}, Size: " . redco_diagnostic_format_bytes($backup_data['size']) . ")");
        
        // Cleanup old backups
        $this->cleanup_old_backups();
        
        return $backup_id;
    }
    
    /**
     * Backup critical files
     */
    private function backup_critical_files($backup_path) {
        $files_backed_up = array();
        
        // wp-config.php
        if (file_exists(ABSPATH . 'wp-config.php')) {
            $wp_config_backup = $backup_path . 'wp-config.php';
            if (copy(ABSPATH . 'wp-config.php', $wp_config_backup)) {
                $files_backed_up['wp_config'] = $wp_config_backup;
            }
        }
        
        // Active theme's functions.php
        $theme_dir = get_template_directory();
        $functions_file = $theme_dir . '/functions.php';
        if (file_exists($functions_file)) {
            $functions_backup = $backup_path . 'functions.php';
            if (copy($functions_file, $functions_backup)) {
                $files_backed_up['functions'] = $functions_backup;
            }
        }
        
        return $files_backed_up;
    }
    
    /**
     * Backup settings
     */
    private function backup_settings() {
        $settings_backup = array();
        
        // WordPress core settings
        $core_options = array(
            'active_plugins',
            'stylesheet',
            'template',
            'blogname',
            'blogdescription'
        );
        
        foreach ($core_options as $option) {
            $settings_backup['core'][$option] = get_option($option);
        }
        
        // Plugin-specific settings
        $plugin_options = array(
            'redco_diagnostic_options',
            'redco_diagnostic_settings'
        );
        
        foreach ($plugin_options as $option) {
            $settings_backup['plugin'][$option] = get_option($option);
        }
        
        return $settings_backup;
    }
    
    /**
     * Calculate backup size
     */
    private function calculate_backup_size($backup_path) {
        $size = 0;
        
        if (is_dir($backup_path)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backup_path, RecursiveDirectoryIterator::SKIP_DOTS)
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        }
        
        return $size;
    }
    
    /**
     * Save backup metadata to database
     */
    private function save_backup_metadata($backup_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $wpdb->insert(
            $table_name,
            array(
                'backup_id' => $backup_data['backup_id'],
                'type' => $backup_data['type'],
                'description' => $backup_data['description'],
                'created_at' => $backup_data['created_at'],
                'created_by' => $backup_data['created_by'],
                'size' => $backup_data['size'],
                'fix_context' => json_encode($backup_data['fix_context']),
                'backup_data' => json_encode($backup_data)
            ),
            array('%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s')
        );
    }
    
    /**
     * Get backup information
     */
    public function get_backup_info($backup_id) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        $backup = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$table_name} WHERE backup_id = %s", $backup_id),
            ARRAY_A
        );
        
        if ($backup) {
            $backup['size_formatted'] = redco_diagnostic_format_bytes($backup['size']);
            $backup['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($backup['created_at']));
            $backup['fix_context'] = json_decode($backup['fix_context'], true);
        }
        
        return $backup;
    }
    
    /**
     * Create automatic backup before applying fixes
     */
    public function create_automatic_backup($fix_id, $fix_context) {
        try {
            $description = "Automatic backup before applying fix: {$fix_id}";
            $backup_id = $this->create_backup('automatic', $description, $fix_context);
            
            redco_diagnostic_log("Automatic backup created before fix application (Backup ID: {$backup_id}, Fix: {$fix_id})");
            
            return $backup_id;
        } catch (Exception $e) {
            redco_diagnostic_log("Failed to create automatic backup: " . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * Cleanup old backups
     */
    public function cleanup_old_backups() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        
        // Get backups older than the limit
        $old_backups = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT backup_id FROM {$table_name} ORDER BY created_at DESC LIMIT %d, 999999",
                $this->max_backups
            )
        );
        
        foreach ($old_backups as $backup) {
            $this->delete_backup($backup->backup_id);
        }
    }
    
    /**
     * Delete backup
     */
    public function delete_backup($backup_id) {
        global $wpdb;
        
        // Delete backup files
        $backup_path = $this->backup_dir . $backup_id . '/';
        if (is_dir($backup_path)) {
            $this->delete_directory($backup_path);
        }
        
        // Delete from database
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        $wpdb->delete($table_name, array('backup_id' => $backup_id), array('%s'));
        
        redco_diagnostic_log("Backup deleted (ID: {$backup_id})");
    }
    
    /**
     * Delete directory recursively
     */
    private function delete_directory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->delete_directory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * Maybe create backup table
     */
    private function maybe_create_backup_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_diagnostic_backups';
        $charset_collate = $wpdb->get_charset_collate();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                backup_id varchar(50) NOT NULL,
                type varchar(20) NOT NULL DEFAULT 'manual',
                description text,
                created_at datetime NOT NULL,
                created_by bigint(20) unsigned NOT NULL,
                size bigint(20) unsigned NOT NULL DEFAULT 0,
                fix_context text,
                backup_data longtext,
                PRIMARY KEY (id),
                UNIQUE KEY backup_id (backup_id),
                KEY created_at (created_at),
                KEY type (type)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
    
    // Placeholder methods for AJAX handlers
    public function ajax_restore_backup() {
        wp_send_json_error('Restore functionality not implemented yet');
    }
    
    public function ajax_delete_backup() {
        wp_send_json_error('Delete functionality not implemented yet');
    }
    
    public function ajax_list_backups() {
        wp_send_json_success(array('backups' => array()));
    }
}
