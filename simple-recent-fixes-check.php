<?php
/**
 * Simple check for Recent Fixes issues without memory-intensive operations
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== SIMPLE RECENT FIXES CHECK ===\n";

// Check 1: Fix history in database
echo "\n1. FIX HISTORY CHECK:\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Sessions in database: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    echo "Latest session:\n";
    echo "  - Timestamp: " . date('Y-m-d H:i:s', $latest_session['timestamp']) . "\n";
    echo "  - Fixes applied: " . $latest_session['fixes_applied'] . "\n";
    echo "  - Rollback ID: " . ($latest_session['rollback_id'] ?? 'NONE') . "\n";
    echo "✅ Fix history exists\n";
} else {
    echo "❌ No fix history found\n";
}

// Check 2: Backup directories
echo "\n2. BACKUP DIRECTORY CHECK:\n";
$backup_base_dir = 'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-backups/';
echo "Backup directory: $backup_base_dir\n";
echo "Directory exists: " . (is_dir($backup_base_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($backup_base_dir)) {
    $backup_dirs = glob($backup_base_dir . 'backup_*', GLOB_ONLYDIR);
    echo "Backup directories found: " . count($backup_dirs) . "\n";
    
    if (!empty($backup_dirs)) {
        echo "Recent backups:\n";
        foreach (array_slice($backup_dirs, -3) as $dir) {
            echo "  - " . basename($dir) . "\n";
        }
        echo "✅ Backup directories exist\n";
    } else {
        echo "❌ No backup directories found\n";
    }
} else {
    echo "❌ Backup directory does not exist\n";
}

// Check 3: Recent Fixes AJAX method exists
echo "\n3. AJAX METHOD CHECK:\n";
try {
    require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
    
    if (class_exists('Redco_Diagnostic_AutoFix')) {
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        $reflection = new ReflectionClass($diagnostic);
        if ($reflection->hasMethod('ajax_load_recent_fixes')) {
            echo "✅ AJAX method exists\n";
            
            // Check if method is accessible
            $method = $reflection->getMethod('ajax_load_recent_fixes');
            echo "Method is public: " . ($method->isPublic() ? 'YES' : 'NO') . "\n";
        } else {
            echo "❌ AJAX method missing\n";
        }
    } else {
        echo "❌ Diagnostic class not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking AJAX method: " . $e->getMessage() . "\n";
}

// Check 4: WordPress AJAX hooks
echo "\n4. WORDPRESS AJAX HOOKS CHECK:\n";
global $wp_filter;

$ajax_action = 'wp_ajax_redco_load_recent_fixes';
if (isset($wp_filter[$ajax_action])) {
    echo "✅ AJAX hook registered: $ajax_action\n";
    echo "Hook callbacks: " . count($wp_filter[$ajax_action]->callbacks) . "\n";
} else {
    echo "❌ AJAX hook not registered: $ajax_action\n";
}

// Check 5: Simple HTML generation test
echo "\n5. HTML GENERATION TEST:\n";
try {
    if (!empty($fix_history)) {
        $test_session = end($fix_history);
        
        // Simple HTML generation (mimicking the AJAX response)
        $html = '<div class="fix-item">';
        $html .= '<div class="fix-header">';
        $html .= '<div class="fix-date">' . human_time_diff($test_session['timestamp']) . ' ago</div>';
        $html .= '<div class="fix-count">' . $test_session['fixes_applied'] . ' fixes</div>';
        $html .= '</div>';
        $html .= '<div class="fix-backup">';
        $html .= '<span class="dashicons dashicons-backup"></span>';
        $html .= '<span>Backup created</span>';
        if (!empty($test_session['rollback_id'])) {
            $html .= '<button type="button" class="button-link rollback-fix" data-backup-id="' . $test_session['rollback_id'] . '">';
            $html .= '<span class="dashicons dashicons-undo"></span>Rollback</button>';
        }
        $html .= '</div>';
        $html .= '</div>';
        
        echo "Generated HTML length: " . strlen($html) . " characters\n";
        echo "Contains fix-item class: " . (strpos($html, 'fix-item') !== false ? 'YES' : 'NO') . "\n";
        echo "Contains rollback button: " . (strpos($html, 'rollback-fix') !== false ? 'YES' : 'NO') . "\n";
        echo "✅ HTML generation working\n";
    } else {
        echo "❌ Cannot test HTML generation - no fix history\n";
    }
} catch (Exception $e) {
    echo "❌ HTML generation failed: " . $e->getMessage() . "\n";
}

// Check 6: JavaScript file exists
echo "\n6. JAVASCRIPT FILE CHECK:\n";
$js_file = 'modules/diagnostic-autofix/assets/diagnostic-autofix.js';
if (file_exists($js_file)) {
    echo "✅ JavaScript file exists: $js_file\n";
    echo "File size: " . number_format(filesize($js_file)) . " bytes\n";
    
    // Check for key functions
    $js_content = file_get_contents($js_file);
    $has_load_recent_fixes = strpos($js_content, 'loadRecentFixes') !== false;
    $has_display_recent_fixes = strpos($js_content, 'displayRecentFixesData') !== false;
    
    echo "Contains loadRecentFixes: " . ($has_load_recent_fixes ? 'YES' : 'NO') . "\n";
    echo "Contains displayRecentFixesData: " . ($has_display_recent_fixes ? 'YES' : 'NO') . "\n";
} else {
    echo "❌ JavaScript file missing: $js_file\n";
}

// Summary and recommendations
echo "\n=== SUMMARY AND RECOMMENDATIONS ===\n";

$issues_found = array();
$fixes_needed = array();

if (empty($fix_history)) {
    $issues_found[] = "No fix history in database";
    $fixes_needed[] = "Apply a test fix to create fix history";
}

if (!is_dir($backup_base_dir)) {
    $issues_found[] = "Backup directory missing";
    $fixes_needed[] = "Create backup directory: $backup_base_dir";
}

if (!isset($wp_filter[$ajax_action])) {
    $issues_found[] = "AJAX hook not registered";
    $fixes_needed[] = "Check if diagnostic module is properly loaded";
}

if (empty($issues_found)) {
    echo "✅ ALL BACKEND SYSTEMS APPEAR FUNCTIONAL\n";
    echo "\nIf Recent Fixes still appear empty in browser:\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Hard refresh page (Ctrl+F5)\n";
    echo "3. Clear browser cache\n";
    echo "4. Check if Recent Fixes container exists in HTML\n";
    echo "5. Verify AJAX requests are being made in Network tab\n";
} else {
    echo "❌ ISSUES FOUND:\n";
    foreach ($issues_found as $issue) {
        echo "  - $issue\n";
    }
    
    echo "\n🔧 FIXES NEEDED:\n";
    foreach ($fixes_needed as $fix) {
        echo "  - $fix\n";
    }
}

echo "\n=== CHECK COMPLETE ===\n";
