<?php
/**
 * Verify that the diagnostic autofix system is in a clean state
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== VERIFYING CLEAN STATE ===\n";

$clean_state = true;

// Check 1: Fix history
echo "\n1. CHECKING FIX HISTORY:\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
if (empty($fix_history)) {
    echo "  ✅ Fix history is clean (empty)\n";
} else {
    echo "  ❌ Fix history still contains " . count($fix_history) . " entries\n";
    $clean_state = false;
}

// Check 2: Diagnostic results
echo "\n2. CHECKING DIAGNOSTIC RESULTS:\n";
$diagnostic_results = get_option('redco_diagnostic_results');
if ($diagnostic_results === false) {
    echo "  ✅ Diagnostic results are clean (not found)\n";
} else {
    echo "  ❌ Diagnostic results still exist\n";
    $clean_state = false;
}

// Check 3: Backup directories
echo "\n3. CHECKING BACKUP DIRECTORIES:\n";
$backup_directories = array(
    'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-backups/',
    'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-cachediagnostic-backups/',
    'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-diagnostic/backups/'
);

foreach ($backup_directories as $backup_dir) {
    if (is_dir($backup_dir)) {
        $backups = glob($backup_dir . 'backup_*');
        if (empty($backups)) {
            echo "  ✅ $backup_dir is clean (no backups)\n";
        } else {
            echo "  ❌ $backup_dir still contains " . count($backups) . " backups\n";
            $clean_state = false;
        }
    } else {
        echo "  ✅ $backup_dir does not exist\n";
    }
}

// Check 4: .htaccess file
echo "\n4. CHECKING .HTACCESS FILE:\n";
$htaccess_file = ABSPATH . '.htaccess';
if (file_exists($htaccess_file)) {
    $htaccess_content = file_get_contents($htaccess_file);
    
    $security_patterns = array(
        'REDCO Security Headers',
        'Security Headers - Added by Redco Optimizer',
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Referrer-Policy',
        'Strict-Transport-Security'
    );
    
    $found_security_headers = false;
    foreach ($security_patterns as $pattern) {
        if (strpos($htaccess_content, $pattern) !== false) {
            $found_security_headers = true;
            break;
        }
    }
    
    if (!$found_security_headers) {
        echo "  ✅ .htaccess is clean (no security headers)\n";
    } else {
        echo "  ❌ .htaccess still contains security headers\n";
        $clean_state = false;
    }
} else {
    echo "  ✅ .htaccess file does not exist\n";
}

// Check 5: Database options
echo "\n5. CHECKING DATABASE OPTIONS:\n";
$diagnostic_options = array(
    'redco_diagnostic_stats',
    'redco_fixed_issues',
    'redco_recent_fixes',
    'redco_scan_progress',
    'redco_last_scan_time',
    'redco_diagnostic_cache',
    'redco_autofix_settings',
    'redco_backup_settings'
);

$remaining_options = 0;
foreach ($diagnostic_options as $option) {
    if (get_option($option) !== false) {
        echo "  ❌ Option still exists: $option\n";
        $remaining_options++;
        $clean_state = false;
    }
}

if ($remaining_options === 0) {
    echo "  ✅ All diagnostic options are clean\n";
} else {
    echo "  ❌ $remaining_options diagnostic options still exist\n";
}

// Check 6: Transients
echo "\n6. CHECKING TRANSIENTS:\n";
global $wpdb;
$transients = $wpdb->get_results(
    "SELECT option_name FROM {$wpdb->options} 
     WHERE option_name LIKE '_transient_redco_%' 
     AND option_name NOT LIKE '%timeout%'"
);

if (empty($transients)) {
    echo "  ✅ No diagnostic transients found\n";
} else {
    echo "  ❌ Found " . count($transients) . " diagnostic transients:\n";
    foreach ($transients as $transient) {
        echo "    - " . str_replace('_transient_', '', $transient->option_name) . "\n";
    }
    $clean_state = false;
}

// Check 7: Engine initialization
echo "\n7. CHECKING ENGINE INITIALIZATION:\n";
try {
    require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    echo "  ✅ Engine can be initialized successfully\n";
    
    // Check backup directory
    $engine_reflection = new ReflectionClass($engine);
    $backup_dir_property = $engine_reflection->getProperty('backup_dir');
    $backup_dir_property->setAccessible(true);
    $engine_backup_dir = $backup_dir_property->getValue($engine);
    echo "  ℹ️  Engine backup directory: $engine_backup_dir\n";
    
} catch (Exception $e) {
    echo "  ❌ Engine initialization failed: " . $e->getMessage() . "\n";
    $clean_state = false;
}

// Final summary
echo "\n=== CLEAN STATE VERIFICATION SUMMARY ===\n";

if ($clean_state) {
    echo "🎉 SYSTEM IS IN COMPLETELY CLEAN STATE!\n";
    echo "\nThe diagnostic autofix system has been successfully reset:\n";
    echo "✅ No fix history records\n";
    echo "✅ No diagnostic results cache\n";
    echo "✅ No backup files\n";
    echo "✅ No security headers in .htaccess\n";
    echo "✅ No database options\n";
    echo "✅ No transients\n";
    echo "✅ Engine can be initialized\n";
    echo "\n🚀 READY FOR FRESH TESTING!\n";
    echo "\nNext steps:\n";
    echo "1. Go to the diagnostic autofix page\n";
    echo "2. Run a new diagnostic scan\n";
    echo "3. Apply some fixes to test backup creation\n";
    echo "4. Test rollback functionality\n";
    echo "5. Verify session mapping works correctly\n";
} else {
    echo "⚠️  SYSTEM IS MOSTLY CLEAN BUT SOME ISSUES REMAIN\n";
    echo "Please review the issues above and clean them manually if needed.\n";
    echo "The system should still be functional for testing.\n";
}

echo "\n=== VERIFICATION COMPLETE ===\n";
