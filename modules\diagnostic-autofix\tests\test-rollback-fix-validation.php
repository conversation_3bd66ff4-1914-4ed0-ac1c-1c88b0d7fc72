<?php
/**
 * Enhanced Test Script for Rollback Button Fix Validation
 * 
 * This script will test the complete backup creation and validation workflow
 * 
 * SECURITY: This file should only be accessible to administrators
 */

// Prevent direct access and ensure WordPress is loaded
if (!defined('ABSPATH')) {
    // Get the WordPress root directory (go up from plugin directory)
    $wp_root = dirname(dirname(dirname(dirname(__FILE__))));
    require_once $wp_root . '/wp-config.php';
}

// Security check - only allow admin users to run this test
if (!current_user_can('manage_options')) {
    wp_die('Access denied. This test can only be run by administrators.');
}

echo "<h1>🔧 Enhanced Rollback Button Fix Validation</h1>\n";

// Initialize required classes
if (!class_exists('Redco_Diagnostic_AutoFix')) {
    require_once dirname(dirname(__FILE__)) . '/class-diagnostic-autofix.php';
}

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    require_once dirname(dirname(__FILE__)) . '/class-diagnostic-autofix-engine.php';
}

$diagnostic = new Redco_Diagnostic_AutoFix();
$engine = new Redco_Diagnostic_AutoFix_Engine();

echo "<h2>📊 Step 1: Testing Backup Directory Detection</h2>\n";

// Use reflection to test the backup directory detection
$reflection = new ReflectionClass($diagnostic);
$get_backup_dirs_method = $reflection->getMethod('get_possible_backup_directories');
$get_backup_dirs_method->setAccessible(true);

$backup_dirs = $get_backup_dirs_method->invoke($diagnostic);
echo "Possible backup directories found: " . count($backup_dirs) . "\n";

foreach ($backup_dirs as $index => $dir) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    echo "  " . ($index + 1) . ". {$dir} - " . ($exists ? 'EXISTS' : 'NOT FOUND') . 
         ($writable ? ' (WRITABLE)' : ($exists ? ' (NOT WRITABLE)' : '')) . "\n";
    
    if ($exists) {
        $files = scandir($dir);
        $backup_count = 0;
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && (is_dir($dir . $file) || strpos($file, 'backup_') === 0)) {
                $backup_count++;
            }
        }
        echo "     Contains {$backup_count} backup-related items\n";
    }
}

echo "\n<h2>🧪 Step 2: Testing Backup Creation Process</h2>\n";

// Create a test issue and apply a fix to test backup creation
$test_issue = array(
    'id' => 'test_rollback_validation_' . time(),
    'title' => 'Test Issue for Rollback Validation',
    'description' => 'Test issue to validate rollback functionality',
    'severity' => 'low',
    'category' => 'test',
    'fix_action' => 'test_fix',
    'auto_fixable' => true
);

echo "Creating test issue: {$test_issue['id']}\n";
echo "Applying fix with backup creation...\n";

$fix_result = $engine->apply_fix($test_issue);

echo "Fix Result:\n";
echo "  Success: " . ($fix_result['success'] ? 'YES' : 'NO') . "\n";
echo "  Message: " . $fix_result['message'] . "\n";

if (isset($fix_result['rollback_id'])) {
    echo "  Rollback ID: " . $fix_result['rollback_id'] . "\n";
    
    // Test backup validation
    echo "\nTesting backup validation for: " . $fix_result['rollback_id'] . "\n";
    
    $validate_method = $reflection->getMethod('validate_backup_exists');
    $validate_method->setAccessible(true);
    
    $backup_exists = $validate_method->invoke($diagnostic, $fix_result['rollback_id']);
    echo "Backup validation result: " . ($backup_exists ? 'VALID' : 'INVALID') . "\n";
    
    if (!$backup_exists) {
        echo "❌ Backup validation failed - investigating...\n";
        
        // Check each possible location
        foreach ($backup_dirs as $dir) {
            if (!is_dir($dir)) continue;
            
            $backup_subdir = $dir . $fix_result['rollback_id'] . '/';
            $backup_file = $dir . $fix_result['rollback_id'] . '.json';
            
            echo "  Checking {$dir}:\n";
            echo "    Subdirectory {$backup_subdir}: " . (is_dir($backup_subdir) ? 'EXISTS' : 'NOT FOUND') . "\n";
            echo "    File {$backup_file}: " . (file_exists($backup_file) ? 'EXISTS' : 'NOT FOUND') . "\n";
            
            if (is_dir($backup_subdir)) {
                $metadata_file = $backup_subdir . 'backup_data.json';
                echo "    Metadata file: " . (file_exists($metadata_file) ? 'EXISTS' : 'NOT FOUND') . "\n";
            }
        }
    } else {
        echo "✅ Backup validation successful\n";
    }
} else {
    echo "  ❌ No rollback ID in fix result\n";
}

echo "\n<h2>🔄 Step 3: Testing Migration Function</h2>\n";

echo "Running migration function...\n";
$migrate_method = $reflection->getMethod('migrate_fix_sessions_rollback_ids');
$migrate_method->setAccessible(true);

$migration_count = $migrate_method->invoke($diagnostic);
echo "Migration completed. Sessions migrated: {$migration_count}\n";

echo "\n<h2>📋 Step 4: Testing Recent Fixes Display</h2>\n";

// Test the AJAX load recent fixes
echo "Testing AJAX load recent fixes...\n";

$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            echo "AJAX Response Analysis:\n";
            
            // Count elements
            $rollback_buttons = substr_count($html, 'rollback-fix');
            $unavailable_messages = substr_count($html, 'rollback-unavailable');
            $backup_created_text = substr_count($html, 'Backup created');
            
            echo "  Rollback buttons: {$rollback_buttons}\n";
            echo "  Unavailable messages: {$unavailable_messages}\n";
            echo "  'Backup created' indicators: {$backup_created_text}\n";
            
            // Extract rollback IDs from buttons
            if (preg_match_all('/data-backup-id="([^"]+)"/', $html, $matches)) {
                echo "  Rollback IDs in buttons:\n";
                foreach ($matches[1] as $backup_id) {
                    echo "    - {$backup_id}\n";
                    
                    // Validate each backup ID
                    $is_valid = $validate_method->invoke($diagnostic, $backup_id);
                    echo "      Validation: " . ($is_valid ? 'VALID' : 'INVALID') . "\n";
                }
            }
            
            // Show sample HTML
            echo "\nSample HTML (first 300 chars):\n";
            echo "<pre>" . htmlspecialchars(substr($html, 0, 300)) . "...</pre>\n";
            
        } else {
            echo "❌ AJAX call failed\n";
            if (isset($response_data['data'])) {
                echo "Error: " . $response_data['data'] . "\n";
            }
        }
    } else {
        echo "❌ AJAX returned empty response\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX exception: " . $e->getMessage() . "\n";
}

echo "\n<h2>🔍 Step 5: Analyzing Fix History Structure</h2>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions: " . count($fix_history) . "\n\n";

if (!empty($fix_history)) {
    // Analyze the most recent sessions
    $recent_sessions = array_slice(array_reverse($fix_history), 0, 3);
    
    foreach ($recent_sessions as $index => $session) {
        echo "Session " . ($index + 1) . " Analysis:\n";
        echo "  Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "\n";
        echo "  Backup Created: " . (isset($session['backup_created']) && $session['backup_created'] ? 'YES' : 'NO') . "\n";
        echo "  Rollback ID: " . (isset($session['rollback_id']) ? $session['rollback_id'] : 'NOT SET') . "\n";
        
        if (isset($session['rollback_id'])) {
            $backup_valid = $validate_method->invoke($diagnostic, $session['rollback_id']);
            echo "  Backup Valid: " . ($backup_valid ? 'YES' : 'NO') . "\n";
        }
        
        echo "  Fixes Applied: " . (isset($session['fixes_applied']) ? $session['fixes_applied'] : 'UNKNOWN') . "\n";
        echo "  Details Count: " . (isset($session['details']) ? count($session['details']) : 'NONE') . "\n";
        echo "\n";
    }
}

echo "<h2>📊 Step 6: Summary and Recommendations</h2>\n";

$issues_found = array();
$fixes_working = array();

// Check if backup creation is working
if (isset($fix_result['rollback_id']) && !empty($fix_result['rollback_id'])) {
    $fixes_working[] = "Backup creation during fix application is working";
} else {
    $issues_found[] = "Backup creation during fix application is not working";
}

// Check if validation is working
if (isset($fix_result['rollback_id'])) {
    $backup_exists = $validate_method->invoke($diagnostic, $fix_result['rollback_id']);
    if ($backup_exists) {
        $fixes_working[] = "Backup validation logic is working correctly";
    } else {
        $issues_found[] = "Backup validation logic is not finding created backups";
    }
}

// Check if migration is working
if ($migration_count > 0) {
    $fixes_working[] = "Migration function is finding and fixing sessions";
} else {
    $issues_found[] = "Migration function found no sessions to fix (may be normal)";
}

// Check AJAX response
if (isset($rollback_buttons) && $rollback_buttons > 0) {
    $fixes_working[] = "AJAX response is generating rollback buttons";
} else {
    $issues_found[] = "AJAX response is not generating rollback buttons";
}

echo "✅ Working Components:\n";
foreach ($fixes_working as $fix) {
    echo "  - {$fix}\n";
}

if (!empty($issues_found)) {
    echo "\n🚨 Issues Found:\n";
    foreach ($issues_found as $issue) {
        echo "  - {$issue}\n";
    }
} else {
    echo "\n🎉 No issues found! Rollback functionality should be working correctly.\n";
}

echo "\n📋 Next Steps:\n";
echo "1. Check the WordPress error log for any REDCO BACKUP or REDCO MIGRATION messages\n";
echo "2. Verify that the backup directories have proper write permissions\n";
echo "3. Test the actual rollback functionality by clicking a rollback button\n";
echo "4. Monitor the Recent Fixes section for proper button display\n";

echo "\n<h3>🧹 Cleanup</h3>\n";
echo "Test completed. The test issue has been processed and should appear in fix history.\n";
echo "Test issue ID: {$test_issue['id']}\n";

// Add instructions for running this test safely
echo "\n<h3>📋 How to Run This Test</h3>\n";
echo "1. Access this test via WordPress admin dashboard\n";
echo "2. Navigate to: /wp-admin/admin.php?page=redco-diagnostic-test-rollback\n";
echo "3. Or run directly: /wp-content/plugins/redco-optimizer/modules/diagnostic-autofix/tests/test-rollback-fix-validation.php\n";
echo "4. Check WordPress error log for detailed debugging information\n";
