<?php
/**
 * Diagnostic & Auto-Fix Module for Redco Optimizer
 *
 * Comprehensive WordPress performance diagnostic tool with intelligent auto-fix capabilities
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include helper methods
require_once __DIR__ . '/class-diagnostic-helpers.php';
require_once __DIR__ . '/class-diagnostic-autofix-engine.php';

// CRITICAL FIX: Ensure main helpers are loaded for redco_format_bytes function
if (!function_exists('redco_format_bytes')) {
    require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/helpers.php';
}

class Redco_Diagnostic_AutoFix {
    use Redco_Diagnostic_Helpers;

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Diagnostic results
     */
    private $diagnostic_results = array();

    /**
     * Fix history
     */
    private $fix_history = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('diagnostic-autofix')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();

        // Enqueue admin assets
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
    }

    /**
     * PERFORMANCE OPTIMIZED: Load module settings with caching
     */
    private function load_settings() {
        // PERFORMANCE: Cache settings for 5 minutes to reduce database calls
        $cache_key = 'redco_diagnostic_settings';
        $cached_settings = wp_cache_get($cache_key, 'redco_optimizer');

        if ($cached_settings !== false) {
            $this->settings = $cached_settings;
            return;
        }

        // PERFORMANCE: Single database call instead of multiple option calls
        $all_options = get_option('redco_optimizer_modules', array());
        $module_settings = isset($all_options['diagnostic-autofix']['settings'])
            ? $all_options['diagnostic-autofix']['settings']
            : array();

        $this->settings = array_merge(array(
            'auto_scan_frequency' => 'weekly',
            'auto_fix_enabled' => false,
            'backup_before_fix' => true,
            'emergency_mode_threshold' => 40,
            'scan_types' => array('wordpress', 'database', 'frontend', 'security'),
            'pagespeed_api_key' => '',
            'notification_email' => get_option('admin_email'),
            'max_fix_batch_size' => 5
        ), $module_settings);

        // PERFORMANCE: Cache for 5 minutes
        wp_cache_set($cache_key, $this->settings, 'redco_optimizer', 300);
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_redco_run_diagnostic_scan', array($this, 'ajax_run_diagnostic_scan'));
        add_action('wp_ajax_redco_apply_auto_fixes', array($this, 'ajax_apply_auto_fixes'));
        add_action('wp_ajax_redco_apply_single_fix', array($this, 'ajax_apply_single_fix'));
        add_action('wp_ajax_redco_rollback_fixes', array($this, 'ajax_rollback_fixes'));
        add_action('wp_ajax_redco_emergency_mode', array($this, 'ajax_emergency_mode'));
        add_action('wp_ajax_redco_export_diagnostic_report', array($this, 'ajax_export_diagnostic_report'));
        add_action('wp_ajax_redco_load_recent_fixes', array($this, 'ajax_load_recent_fixes'));
        add_action('wp_ajax_redco_get_real_metrics', array($this, 'ajax_get_real_metrics'));

        add_action('wp_ajax_redco_get_how_to_solve_tip', array($this, 'ajax_get_how_to_solve_tip'));

        // NEW: Real optimization fix handlers
        add_action('wp_ajax_redco_apply_optimization_fix', array($this, 'ajax_apply_optimization_fix'));
        add_action('wp_ajax_redco_scan_optimization_opportunities', array($this, 'ajax_scan_optimization_opportunities'));
        add_action('wp_ajax_redco_apply_all_optimization_fixes', array($this, 'ajax_apply_all_optimization_fixes'));

        // CRITICAL FIX: Emergency recovery handler
        add_action('wp_ajax_redco_emergency_recovery_report', array($this, 'ajax_emergency_recovery_report'));

        // Scheduled scans
        add_action('redco_scheduled_diagnostic_scan', array($this, 'run_scheduled_scan'));

        // Admin notices for critical issues - DISABLED to prevent layout shifts
        // add_action('admin_notices', array($this, 'show_critical_issue_notices'));

        // Schedule automatic scans if enabled
        if (!wp_next_scheduled('redco_scheduled_diagnostic_scan')) {
            $frequency = $this->settings['auto_scan_frequency'];
            wp_schedule_event(time(), $frequency, 'redco_scheduled_diagnostic_scan');
        }

        // Initialize Phase 1 enhancements
        $this->init_phase1_enhancements();
    }

    /**
     * Initialize Phase 1 enhancement components
     */
    private function init_phase1_enhancements() {
        // Initialize tiered fix system
        if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/safety/class-tiered-fix-system.php')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/safety/class-tiered-fix-system.php';
            $tiered_system = new Redco_Tiered_Fix_System();
            $tiered_system->init();
        }

        // Initialize fix scheduler
        if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/scheduling/class-fix-scheduler.php')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/scheduling/class-fix-scheduler.php';
            $scheduler = new Redco_Fix_Scheduler();
            $scheduler->init();
        }

        // Initialize enhanced backup system
        if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/safety/class-enhanced-backup.php')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/safety/class-enhanced-backup.php';
            $backup_system = new Redco_Enhanced_Backup();
            $backup_system->init();
        }

        // Add missing AJAX endpoints for Phase 1 features
        add_action('wp_ajax_redco_get_scheduled_fixes', array($this, 'ajax_get_scheduled_fixes'));
        add_action('wp_ajax_redco_cancel_scheduled_fix', array($this, 'ajax_cancel_scheduled_fix'));

        // Add missing AJAX endpoints that validation expects
        add_action('wp_ajax_redco_get_recent_fixes', array($this, 'ajax_get_recent_fixes'));
        add_action('wp_ajax_redco_get_diagnostic_results', array($this, 'ajax_get_diagnostic_results'));

        // Add AJAX endpoint that JavaScript expects (high priority to override other handlers)
        add_action('wp_ajax_redco_get_performance_metrics', array($this, 'ajax_get_performance_metrics'), 5);

        // DEVELOPMENT HELPER: Clear all diagnostic data
        add_action('wp_ajax_redco_clear_all_diagnostic_data', array($this, 'ajax_clear_all_diagnostic_data'));
        add_action('wp_ajax_redco_simple_clear_diagnostic_data', array($this, 'ajax_simple_clear_diagnostic_data'));

        // EMERGENCY HELPER: Rollback .htaccess security headers
        add_action('wp_ajax_redco_rollback_htaccess_security_headers', array($this, 'ajax_rollback_htaccess_security_headers'));

        // EMERGENCY CONTROL: Reset all fixes and diagnostic data
        add_action('wp_ajax_redco_reset_all_fixes', array($this, 'ajax_reset_all_fixes'));

        // CRITICAL FIX: Clear fixed issues state after rollback
        add_action('wp_ajax_redco_clear_fixed_issues_state', array($this, 'ajax_clear_fixed_issues_state'));

        // Add test endpoint for debugging
        add_action('wp_ajax_redco_test_phase2_ajax', array($this, 'ajax_test_phase2'));

        // Initialize Phase 2 components
        $this->init_phase2_components();

        // Phase 1 validation completed - banner removed
    }

    // Phase 1 validation method removed - validation completed

    /**
     * Initialize Phase 2 components
     */
    private function init_phase2_components() {
        // Initialize Real-Time Monitoring
        if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/monitoring/class-realtime-monitor.php')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/monitoring/class-realtime-monitor.php';
            $realtime_monitor = new Redco_Realtime_Monitor();
            $realtime_monitor->init();
        }

        // Initialize Core Web Vitals
        if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/monitoring/class-core-web-vitals.php')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/monitoring/class-core-web-vitals.php';
            $core_web_vitals = new Redco_Core_Web_Vitals();
            $core_web_vitals->init();
        }

        // Phase 2 validation completed - banner removed
    }

    // Phase 2 validation method removed - validation completed

    /**
     * AJAX: Get scheduled fixes
     */
    public function ajax_get_scheduled_fixes() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        $scheduled_fixes = $wpdb->get_results(
            "SELECT * FROM {$table_name} ORDER BY scheduled_time ASC",
            ARRAY_A
        );

        wp_send_json_success($scheduled_fixes);
    }

    /**
     * AJAX: Cancel scheduled fix
     */
    public function ajax_cancel_scheduled_fix() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $scheduled_fix_id = sanitize_text_field($_POST['scheduled_fix_id'] ?? '');

        if (empty($scheduled_fix_id)) {
            wp_send_json_error('Scheduled fix ID is required');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        $result = $wpdb->update(
            $table_name,
            array('status' => 'cancelled'),
            array('id' => $scheduled_fix_id),
            array('%s'),
            array('%s')
        );

        if ($result !== false) {
            // Remove cron event
            wp_clear_scheduled_hook('redco_execute_scheduled_fix', array($scheduled_fix_id));
            wp_send_json_success('Scheduled fix cancelled successfully');
        } else {
            wp_send_json_error('Failed to cancel scheduled fix');
        }
    }

    /**
     * AJAX: Get recent fixes
     */
    public function ajax_get_recent_fixes() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_fix_history';

        // Check if table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            wp_send_json_success(array(
                'recent_fixes' => array(),
                'total_count' => 0,
                'message' => 'Fix history table not yet created'
            ));
            return;
        }

        // Get recent fixes from the last 30 days
        $recent_fixes = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$table_name}
                 WHERE executed_at >= %s
                 ORDER BY executed_at DESC
                 LIMIT 50",
                date('Y-m-d H:i:s', strtotime('-30 days'))
            ),
            ARRAY_A
        );

        // Format the results
        $formatted_fixes = array();
        foreach ($recent_fixes as $fix) {
            $formatted_fixes[] = array(
                'id' => $fix['id'],
                'fix_id' => $fix['fix_id'],
                'fix_type' => $fix['fix_type'],
                'fix_tier' => $fix['fix_tier'],
                'fix_category' => $fix['fix_category'],
                'executed_at' => $fix['executed_at'],
                'success' => (bool) $fix['success'],
                'error_message' => $fix['error_message'],
                'execution_method' => $fix['execution_method']
            );
        }

        wp_send_json_success(array(
            'recent_fixes' => $formatted_fixes,
            'total_count' => count($formatted_fixes)
        ));
    }

    /**
     * AJAX: Get diagnostic results
     */
    public function ajax_get_diagnostic_results() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Get stored diagnostic results
        $diagnostic_results = get_option('redco_diagnostic_results', array());

        // If no results exist, run a quick scan
        if (empty($diagnostic_results) || !isset($diagnostic_results['issues'])) {
            $diagnostic_results = $this->run_diagnostic_scan();
        }

        // Enhance results with tier data if Phase 1 enhancements are active
        if (class_exists('Redco_Tiered_Fix_System')) {
            $tiered_system = new Redco_Tiered_Fix_System();
            $diagnostic_results['issues'] = $tiered_system->enhance_issues_with_tier_data($diagnostic_results['issues']);
        }

        wp_send_json_success(array(
            'diagnostic_results' => $diagnostic_results,
            'last_scan' => $diagnostic_results['scan_timestamp'] ?? time(),
            'issues_count' => count($diagnostic_results['issues'] ?? array())
        ));
    }

    /**
     * AJAX: Get performance metrics (for JavaScript compatibility)
     */
    public function ajax_get_performance_metrics() {
        // Primary nonce check - JavaScript sends redco_optimizer_nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_optimizer_nonce')) {
            // Fallback to diagnostic nonce for backward compatibility
            if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_diagnostic_nonce')) {
                wp_send_json_error(array(
                    'message' => 'Security verification failed',
                    'handler' => 'Phase 2 Diagnostic System'
                ));
                return;
            }
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Use the real-time monitor if available
        if (class_exists('Redco_Realtime_Monitor')) {
            $monitor = new Redco_Realtime_Monitor();
            $metrics = $monitor->collect_current_metrics();

            // Add health score calculation
            $health_score = $this->calculate_health_score($metrics);
            $metrics['health_score'] = $health_score;

            wp_send_json_success($metrics);
        } else {
            // Fallback basic metrics
            wp_send_json_success(array(
                'page_load_time' => 0,
                'memory_usage' => memory_get_usage(true),
                'database_queries' => 0,
                'cache_hit_ratio' => 0,
                'health_score' => 50,
                'timestamp' => time()
            ));
        }
    }

    /**
     * Calculate overall health score
     */
    private function calculate_health_score($metrics) {
        $score = 100;

        // Deduct points based on performance issues
        foreach ($metrics as $metric_name => $metric_data) {
            if (isset($metric_data['status'])) {
                switch ($metric_data['status']) {
                    case 'poor':
                        $score -= 20;
                        break;
                    case 'needs_improvement':
                        $score -= 10;
                        break;
                    case 'good':
                        // No deduction
                        break;
                }
            }
        }

        return max(0, min(100, $score));
    }

    /**
     * AJAX: Test Phase 2 AJAX system
     */
    public function ajax_test_phase2() {
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_optimizer_nonce')) {
            wp_send_json_error(array(
                'message' => 'Nonce verification failed',
                'nonce_received' => $_POST['nonce'] ?? 'none'
            ));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        wp_send_json_success(array(
            'message' => 'Phase 2 AJAX system working correctly!',
            'timestamp' => time(),
            'nonce_verified' => true,
            'handler' => 'Phase 2 Diagnostic System'
        ));
    }

    /**
     * PERFORMANCE OPTIMIZED: Enqueue admin assets with conditional loading
     */
    public function enqueue_admin_assets($hook) {
        // PERFORMANCE: Early exit for non-plugin pages
        if (strpos($hook, 'redco-optimizer') === false) {
            return;
        }

        // CRITICAL FIX: Load on appropriate pages with proper tab detection
        $current_tab = isset($_GET['tab']) ? $_GET['tab'] : 'dashboard';
        $is_modules_page = strpos($hook, 'redco-optimizer-modules') !== false;
        $is_main_dashboard = strpos($hook, 'redco-optimizer') !== false && !strpos($hook, 'modules');
        $is_diagnostic_tab = $current_tab === 'diagnostic-autofix';

        // Load diagnostic assets on:
        // 1. Modules page with diagnostic-autofix tab (full interface)
        // 2. Main dashboard (for dashboard widgets and quick actions)
        $should_load = ($is_modules_page && $is_diagnostic_tab) ||
                       ($is_main_dashboard && redco_is_module_enabled('diagnostic-autofix'));

        if ($should_load) {
            // Enqueue CSS
            wp_enqueue_style(
                'redco-diagnostic-autofix',
                REDCO_OPTIMIZER_PLUGIN_URL . 'modules/diagnostic-autofix/assets/diagnostic-autofix.css',
                array(),
                REDCO_OPTIMIZER_VERSION
            );

            // Enqueue JavaScript with cache-busting
            $js_file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/diagnostic-autofix.js';
            $js_version = REDCO_OPTIMIZER_VERSION . '.' . filemtime($js_file_path);

            wp_enqueue_script(
                'redco-diagnostic-autofix',
                REDCO_OPTIMIZER_PLUGIN_URL . 'modules/diagnostic-autofix/assets/diagnostic-autofix.js',
                array('jquery'),
                $js_version,
                true
            );

            // Enqueue enhanced UI components for Phase 1
            if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/ui/enhanced-ui.js')) {
                wp_enqueue_script(
                    'redco-enhanced-ui',
                    REDCO_OPTIMIZER_PLUGIN_URL . 'modules/diagnostic-autofix/assets/ui/enhanced-ui.js',
                    array('jquery', 'redco-diagnostic-autofix'),
                    $js_version,
                    true
                );
            }

            // Get global debug mode setting with proper type checking
            $global_options = get_option('redco_optimizer_options', array());
            $debug_mode = isset($global_options['debug_mode']) ? (bool) $global_options['debug_mode'] : false;

            // Additional check for checkbox value (WordPress saves checkboxes as '1' or empty)
            if (isset($global_options['debug_mode']) && $global_options['debug_mode'] === '1') {
                $debug_mode = true;
            } elseif (isset($global_options['debug_mode']) && $global_options['debug_mode'] === '') {
                $debug_mode = false;
            }

            // Localize script for AJAX
            wp_localize_script('redco-diagnostic-autofix', 'redcoDiagnosticAjax', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('redco_diagnostic_nonce'),
                'current_tab' => $current_tab,
                'module_enabled' => redco_is_module_enabled('diagnostic-autofix'),
                'debug_toast_notifications' => $debug_mode,
                'strings' => array(
                    'scanning' => __('Running diagnostic scan...', 'redco-optimizer'),
                    'applying_fixes' => __('Applying auto-fixes...', 'redco-optimizer'),
                    'scan_complete' => __('Scan completed successfully!', 'redco-optimizer'),
                    'fixes_applied' => __('Auto-fixes applied successfully!', 'redco-optimizer'),
                    'error' => __('An error occurred. Please try again.', 'redco-optimizer'),
                    'confirm_fixes' => __('Are you sure you want to apply auto-fixes? This action cannot be undone without a rollback.', 'redco-optimizer'),
                    // Phase 1 enhancement strings
                    'preview_loading' => __('Loading preview...', 'redco-optimizer'),
                    'preview_failed' => __('Preview failed to load.', 'redco-optimizer'),
                    'schedule_success' => __('Fix scheduled successfully!', 'redco-optimizer'),
                    'schedule_failed' => __('Failed to schedule fix.', 'redco-optimizer'),
                    'confirm_schedule' => __('Are you sure you want to schedule this fix?', 'redco-optimizer'),
                    'tier_safe' => __('Safe', 'redco-optimizer'),
                    'tier_moderate' => __('Moderate', 'redco-optimizer'),
                    'tier_advanced' => __('Advanced', 'redco-optimizer'),
                    'all_fixes' => __('All Fixes', 'redco-optimizer'),
                    'preview_button' => __('Preview', 'redco-optimizer'),
                    'schedule_button' => __('Schedule', 'redco-optimizer'),
                    'apply_button' => __('Apply Now', 'redco-optimizer')
                )
            ));

            // Also localize for enhanced UI script
            if (file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/assets/ui/enhanced-ui.js')) {
                wp_localize_script('redco-enhanced-ui', 'redcoAjax', array(
                    'ajaxurl' => admin_url('admin-ajax.php'),
                    'nonce' => wp_create_nonce('redco_diagnostic_nonce'),
                    'strings' => array(
                        'preview_loading' => __('Loading preview...', 'redco-optimizer'),
                        'preview_failed' => __('Preview failed to load.', 'redco-optimizer'),
                        'schedule_success' => __('Fix scheduled successfully!', 'redco-optimizer'),
                        'schedule_failed' => __('Failed to schedule fix.', 'redco-optimizer')
                    )
                ));
            }
        }
    }

    /**
     * AJAX handler for running diagnostic scan
     */
    public function ajax_run_diagnostic_scan() {
        // CRITICAL DEBUG: Add error logging to identify the issue
        error_log('🔍 DIAGNOSTIC SCAN AJAX CALLED');

        // CRITICAL FIX: Use correct nonce name that matches JavaScript
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            error_log('❌ NONCE VERIFICATION FAILED');
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'error_code' => 'NONCE_FAILED'
            ));
            return;
        }

        error_log('✅ NONCE VERIFIED');

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            error_log('❌ INSUFFICIENT PERMISSIONS');
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'error_code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return;
        }

        error_log('✅ PERMISSIONS VERIFIED');

        // CRITICAL FIX: Set execution time limit and memory limit for comprehensive scans
        $original_time_limit = ini_get('max_execution_time');
        $original_memory_limit = ini_get('memory_limit');

        // Increase limits for scan operation
        @ini_set('max_execution_time', 300); // 5 minutes
        @ini_set('memory_limit', '512M');

        try {
            error_log('🚀 STARTING DIAGNOSTIC SCAN');

            // CRITICAL DEBUG: Test if basic methods exist
            if (!method_exists($this, 'run_diagnostic_scan')) {
                throw new Exception('run_diagnostic_scan method does not exist');
            }

            // CRITICAL FIX: Clear previous results before starting new scan
            delete_option('redco_diagnostic_results');
            delete_option('redco_diagnostic_stats');

            $scan_type = sanitize_text_field($_POST['scan_type'] ?? 'comprehensive');
            $include_pagespeed = isset($_POST['include_pagespeed']) && !empty($this->settings['pagespeed_api_key']);

            error_log("📊 SCAN PARAMETERS: Type={$scan_type}, PageSpeed=" . ($include_pagespeed ? 'Yes' : 'No'));

            // CRITICAL FIX: Run real diagnostic scan for all scan types
            error_log('🔄 RUNNING REAL DIAGNOSTIC SCAN');
            $results = $this->run_diagnostic_scan($scan_type, $include_pagespeed);
            error_log('✅ REAL SCAN COMPLETED');

        } catch (Exception $e) {
            error_log('❌ SCAN EXCEPTION: ' . $e->getMessage());
            error_log('📍 EXCEPTION TRACE: ' . $e->getTraceAsString());

            // Restore original limits
            @ini_set('max_execution_time', $original_time_limit);
            @ini_set('memory_limit', $original_memory_limit);

            wp_send_json_error(array(
                'message' => 'Scan failed: ' . $e->getMessage(),
                'error_code' => 'SCAN_EXCEPTION',
                'details' => defined('WP_DEBUG') && WP_DEBUG ? $e->getTraceAsString() : null
            ));
            return;
        } catch (Error $e) {
            error_log('❌ FATAL ERROR: ' . $e->getMessage());
            error_log('📍 ERROR TRACE: ' . $e->getTraceAsString());

            // Restore original limits
            @ini_set('max_execution_time', $original_time_limit);
            @ini_set('memory_limit', $original_memory_limit);

            wp_send_json_error(array(
                'message' => 'Fatal error during scan: ' . $e->getMessage(),
                'error_code' => 'FATAL_ERROR',
                'details' => defined('WP_DEBUG') && WP_DEBUG ? $e->getTraceAsString() : null
            ));
            return;
        }

        // Restore original limits
        @ini_set('max_execution_time', $original_time_limit);
        @ini_set('memory_limit', $original_memory_limit);

        // Add summary fields to the results for easier access
        $results['issues_found'] = count($results['issues']);
        $results['critical_issues'] = count(array_filter($results['issues'], function($issue) {
            return $issue['severity'] === 'critical';
        }));
        $results['auto_fixable'] = count(array_filter($results['issues'], function($issue) {
            return $issue['auto_fixable'];
        }));
        $results['scan_timestamp'] = time();
        $results['recommendations'] = $this->generate_recommendations($results);

        error_log('📤 SENDING SCAN RESULTS: ' . count($results['issues']) . ' issues found');

        wp_send_json_success($results);
    }

    /**
     * Run comprehensive diagnostic scan with integrated optimization opportunities
     */
    public function run_diagnostic_scan($scan_type = 'comprehensive', $include_pagespeed = false) {
        // CRITICAL FIX: Add memory and performance monitoring
        $initial_memory = memory_get_usage();
        $peak_memory = memory_get_peak_usage();

        // Always clear previous scan results before new scan
        delete_option('redco_diagnostic_results');
        delete_option('redco_diagnostic_stats');

        // Get list of previously fixed issues to exclude from new scans
        $fixed_issues = get_option('redco_fixed_issues', array());

        $results = array(
            'scan_type' => $scan_type,
            'timestamp' => time(),
            'issues' => array(),
            'optimization_opportunities' => array(), // NEW: Integrated opportunities
            'performance_score' => 0,
            'health_score' => 0,
            'scan_duration' => 0,
            'fixed_issues_excluded' => count($fixed_issues),
            'memory_usage' => array(
                'initial' => $initial_memory,
                'peak' => $peak_memory
            )
        );

        $start_time = microtime(true);

        // CRITICAL FIX: Add progress tracking for long-running scans
        $scan_steps = array();
        if ($scan_type === 'comprehensive') {
            $scan_steps = array('wordpress', 'database', 'frontend', 'server', 'security', 'modules');
        } else {
            $scan_steps = array($scan_type);
        }

        $total_steps = count($scan_steps) + ($include_pagespeed ? 1 : 0);
        $current_step = 0;

        // CRITICAL FIX: Add progress tracking and memory management for each scan step

        // WordPress Core Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'wordpress' || $scan_type === 'quick') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Scanning WordPress core...');
            $results['issues'] = array_merge($results['issues'], $this->scan_wordpress_issues());
            $this->clear_memory_if_needed();
        }

        // Database Performance Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'database') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Analyzing database performance...');
            $results['issues'] = array_merge($results['issues'], $this->scan_database_issues());
            $this->clear_memory_if_needed();
        }

        // Frontend Performance Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'frontend') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Checking frontend optimization...');
            $results['issues'] = array_merge($results['issues'], $this->scan_frontend_issues());
            $this->clear_memory_if_needed();
        }

        // Server Configuration Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'server') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Evaluating server configuration...');
            $results['issues'] = array_merge($results['issues'], $this->scan_server_issues());
            $this->clear_memory_if_needed();
        }

        // Security Configuration Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'security') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Scanning security configuration...');
            $results['issues'] = array_merge($results['issues'], $this->scan_security_issues());
            $this->clear_memory_if_needed();
        }

        // Redco Optimizer Module Issues
        if ($scan_type === 'comprehensive' || $scan_type === 'modules') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Checking module configuration...');
            $results['issues'] = array_merge($results['issues'], $this->scan_module_issues());
            $this->clear_memory_if_needed();
        }

        // PageSpeed Insights Integration (if API key configured)
        if ($include_pagespeed && !empty($this->settings['pagespeed_api_key'])) {
            $pagespeed_results = $this->scan_pagespeed_issues();
            $results['issues'] = array_merge($results['issues'], $pagespeed_results['issues']);
            $results['pagespeed_score'] = $pagespeed_results['score'];
        }

        // CRITICAL FIX: Filter out previously fixed issues that are still resolved
        $results['issues'] = $this->filter_fixed_issues($results['issues'], $fixed_issues);

        // CRITICAL FIX: Clean up obsolete fixed issues that are no longer detected
        $this->cleanup_obsolete_fixed_issues($results['issues']);

        // NEW: Integrate optimization opportunities into comprehensive scan
        if ($scan_type === 'comprehensive') {
            $current_step++;
            $this->update_scan_progress($current_step, $total_steps, 'Scanning optimization opportunities...');
            $results['optimization_opportunities'] = $this->scan_real_optimization_opportunities();
            $this->clear_memory_if_needed();
        }

        // TASK 1: Consolidate opportunities into issues for unified display
        $results = $this->consolidate_opportunities_into_issues($results);

        // TASK 2: Use unified performance score calculation
        $results['health_score'] = $this->calculate_health_score($results['issues'], $results['optimization_opportunities']);
        $results['performance_score'] = $this->calculate_unified_performance_score($results);
        $results['scan_duration'] = round((microtime(true) - $start_time) * 1000); // milliseconds

        // Add summary statistics
        $results['total_opportunities'] = $this->count_total_opportunities($results['optimization_opportunities']);
        $results['auto_fixable_opportunities'] = $this->count_auto_fixable_opportunities($results['optimization_opportunities']);

        // Store results
        $this->diagnostic_results = $results;
        update_option('redco_diagnostic_results', $results);

        return $results;
    }

    /**
     * CRITICAL FIX: Update scan progress for long-running operations
     */
    private function update_scan_progress($current_step, $total_steps, $message) {
        // Store progress in transient for potential AJAX polling
        $progress = array(
            'current_step' => $current_step,
            'total_steps' => $total_steps,
            'percentage' => round(($current_step / $total_steps) * 100),
            'message' => $message,
            'timestamp' => time()
        );

        set_transient('redco_scan_progress', $progress, 300); // 5 minutes

        // Optional: Send progress update via WordPress hooks
        do_action('redco_scan_progress_update', $progress);
    }

    /**
     * CRITICAL FIX: Clear memory if usage is getting high
     */
    private function clear_memory_if_needed() {
        $current_memory = memory_get_usage();
        $memory_limit = $this->get_memory_limit_bytes();

        // If using more than 80% of memory limit, force garbage collection
        if ($current_memory > ($memory_limit * 0.8)) {
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }
        }
    }

    /**
     * CRITICAL FIX: Get memory limit in bytes
     */
    private function get_memory_limit_bytes() {
        $memory_limit = ini_get('memory_limit');

        if (preg_match('/^(\d+)(.)$/', $memory_limit, $matches)) {
            $value = (int) $matches[1];
            $unit = strtoupper($matches[2]);

            switch ($unit) {
                case 'G':
                    return $value * 1024 * 1024 * 1024;
                case 'M':
                    return $value * 1024 * 1024;
                case 'K':
                    return $value * 1024;
                default:
                    return $value;
            }
        }

        return 128 * 1024 * 1024; // Default 128MB
    }

    /**
     * Filter out previously fixed issues that are still resolved
     */
    private function filter_fixed_issues($current_issues, $fixed_issues) {
        if (empty($fixed_issues)) {
            return $current_issues;
        }

        $filtered_issues = array();
        $issues_to_remove_from_fixed = array();

        foreach ($current_issues as $issue) {
            $issue_id = $issue['id'];

            // Check if this issue was previously fixed
            if (isset($fixed_issues[$issue_id])) {
                $fix_info = $fixed_issues[$issue_id];

                // Verify if the fix is still effective
                if ($this->verify_issue_still_fixed($issue, $fix_info)) {
                    // Issue is still fixed, exclude from results
                    error_log("REDCO: Issue {$issue_id} is still fixed, excluding from scan results");
                    continue;
                } else {
                    // Fix is no longer effective, mark for removal from fixed issues list
                    $issues_to_remove_from_fixed[] = $issue_id;
                    error_log("REDCO: Issue {$issue_id} fix is no longer effective, will be removed from fixed list");
                }
            }

            // Include issue in results
            $filtered_issues[] = $issue;
        }

        // Remove issues that are no longer fixed
        if (!empty($issues_to_remove_from_fixed)) {
            foreach ($issues_to_remove_from_fixed as $issue_id) {
                unset($fixed_issues[$issue_id]);
            }
            update_option('redco_fixed_issues', $fixed_issues);
            error_log("REDCO: Removed " . count($issues_to_remove_from_fixed) . " issues from fixed list as they reappeared");
        }

        return $filtered_issues;
    }

    /**
     * Verify if a previously fixed issue is still resolved
     */
    private function verify_issue_still_fixed($issue, $fix_info) {
        // Check based on issue type and fix action (FIXED: Use comprehensive detection)
        switch ($issue['fix_action']) {
            case 'enable_compression':
            case 'enable_gzip_compression':
                return $this->is_compression_enabled_comprehensive();

            case 'set_cache_headers':
                return $this->has_proper_cache_headers();

            case 'optimize_autoload':
                $current_size = $this->get_autoload_size();
                return $current_size < (1024 * 1024); // Less than 1MB

            case 'cleanup_database':
                $current_size = $this->get_database_size();
                return $current_size < $fix_info['original_size'] * 0.9; // 10% reduction maintained

            case 'enable_wp_cache':
                return defined('WP_CACHE') && WP_CACHE;

            case 'fix_render_blocking':
                return !$this->is_resource_still_blocking($issue);

            case 'add_security_header':
                return $this->is_security_header_present($issue);

            default:
                // For unknown fix types, assume still fixed for 24 hours
                return (time() - $fix_info['timestamp']) < (24 * 3600);
        }
    }

    /**
     * Track a successfully fixed issue with comprehensive state management
     */
    public function track_fixed_issue($issue_id, $fix_details) {
        $fixed_issues = get_option('redco_fixed_issues', array());

        // Store comprehensive fix information
        $fixed_issues[$issue_id] = array(
            'timestamp' => time(),
            'fix_details' => $fix_details,
            'original_size' => isset($fix_details['original_size']) ? $fix_details['original_size'] : null,
            'verification_count' => 0,
            'fix_type' => isset($fix_details['fix_type']) ? $fix_details['fix_type'] : 'unknown',
            'rollback_id' => isset($fix_details['rollback_id']) ? $fix_details['rollback_id'] : null,
            'changes_made' => isset($fix_details['changes_made']) ? $fix_details['changes_made'] : array(),
            'issue_title' => isset($fix_details['issue_title']) ? $fix_details['issue_title'] : 'Unknown Issue'
        );

        update_option('redco_fixed_issues', $fixed_issues);

        // Log for debugging
        error_log("REDCO: Tracked fixed issue {$issue_id} with rollback_id: " . ($fixed_issues[$issue_id]['rollback_id'] ?? 'none'));
    }

    /**
     * CRITICAL FIX: Clean up obsolete fixed issues that are no longer detected
     */
    private function cleanup_obsolete_fixed_issues($current_issues) {
        $fixed_issues = get_option('redco_fixed_issues', array());

        if (empty($fixed_issues)) {
            return;
        }

        // Get list of current issue IDs
        $current_issue_ids = array_column($current_issues, 'id');

        $cleaned_up = false;
        foreach ($fixed_issues as $fixed_issue_id => $fix_info) {
            // If a fixed issue is no longer being detected, it's truly resolved
            if (!in_array($fixed_issue_id, $current_issue_ids)) {
                // Keep the fix in history but mark it as permanently resolved
                $fixed_issues[$fixed_issue_id]['permanently_resolved'] = true;
                $fixed_issues[$fixed_issue_id]['resolved_timestamp'] = time();
                $cleaned_up = true;

                error_log("REDCO: Issue {$fixed_issue_id} marked as permanently resolved");
            }
        }

        if ($cleaned_up) {
            update_option('redco_fixed_issues', $fixed_issues);
        }
    }

    /**
     * Check if a resource is still render-blocking
     */
    private function is_resource_still_blocking($issue) {
        // Extract resource URL from issue description
        $resource_url = $this->extract_resource_url_from_description($issue['description']);
        if (!$resource_url) {
            return false; // Can't verify, assume fixed
        }

        // Check if resource is still blocking
        $blocking_resources = $this->detect_render_blocking_resources();
        foreach ($blocking_resources as $resource) {
            if (strpos($resource['url'], $resource_url) !== false) {
                return true; // Still blocking
            }
        }

        return false; // No longer blocking
    }

    /**
     * Check if a security header is present
     */
    private function is_security_header_present($issue) {
        // Extract header name from issue ID
        $header_name = str_replace('missing_security_header_', '', $issue['id']);

        $security_headers = $this->check_security_headers();
        return isset($security_headers[$header_name]) && $security_headers[$header_name]['present'];
    }

    /**
     * Scan WordPress core issues
     */
    private function scan_wordpress_issues() {
        $issues = array();

        // WordPress version check
        $wp_version = get_bloginfo('version');
        $latest_version = $this->get_latest_wordpress_version();

        if (version_compare($wp_version, $latest_version, '<')) {
            $issues[] = array(
                'id' => 'outdated_wordpress',
                'title' => __('Outdated WordPress Version', 'redco-optimizer'),
                'description' => sprintf(__('WordPress %s is available. You are running %s.', 'redco-optimizer'), $latest_version, $wp_version),
                'severity' => 'high',
                'category' => 'wordpress',
                'auto_fixable' => false,
                'impact' => 'Security vulnerabilities and performance issues',
                'recommendation' => 'Update WordPress to the latest version',
                'fix_action' => 'manual_update_wordpress'
            );
        }

        // Plugin conflicts check
        $plugin_conflicts = $this->detect_plugin_conflicts();
        foreach ($plugin_conflicts as $conflict) {
            $issues[] = array(
                'id' => 'plugin_conflict_' . $conflict['plugin'],
                'title' => sprintf(__('Plugin Conflict: %s', 'redco-optimizer'), $conflict['name']),
                'description' => $conflict['description'],
                'severity' => $conflict['severity'],
                'category' => 'wordpress',
                'auto_fixable' => $conflict['auto_fixable'],
                'impact' => $conflict['impact'],
                'recommendation' => $conflict['recommendation'],
                'fix_action' => $conflict['fix_action']
            );
        }

        // Theme performance issues
        $theme_issues = $this->scan_theme_performance();
        $issues = array_merge($issues, $theme_issues);

        // WordPress configuration issues
        $config_issues = $this->scan_wp_config_issues();
        $issues = array_merge($issues, $config_issues);

        return $issues;
    }

    /**
     * Scan database performance issues
     */
    private function scan_database_issues() {
        global $wpdb;
        $issues = array();

        // Database size analysis
        $db_size = $this->get_database_size();
        if ($db_size > 100 * 1024 * 1024) { // 100MB
            $issues[] = array(
                'id' => 'large_database',
                'title' => __('Large Database Size', 'redco-optimizer'),
                'description' => sprintf(__('Database size is %s. Consider cleanup.', 'redco-optimizer'), redco_format_bytes($db_size)),
                'severity' => 'medium',
                'category' => 'database',
                'auto_fixable' => true,
                'impact' => 'Slower database queries and increased backup time',
                'recommendation' => 'Clean up unnecessary data',
                'fix_action' => 'cleanup_database'
            );
        }

        // Slow query detection
        $slow_queries = $this->detect_slow_queries();
        foreach ($slow_queries as $query) {
            $issues[] = array(
                'id' => 'slow_query_' . md5($query['sql']),
                'title' => __('Slow Database Query', 'redco-optimizer'),
                'description' => sprintf(__('Query taking %s seconds: %s', 'redco-optimizer'), $query['time'], substr($query['sql'], 0, 100) . '...'),
                'severity' => $query['time'] > 2 ? 'high' : 'medium',
                'category' => 'database',
                'auto_fixable' => false,
                'impact' => 'Increased page load time',
                'recommendation' => 'Optimize query or add database indexes',
                'fix_action' => 'optimize_query'
            );
        }

        // Autoload data check
        $autoload_size = $this->get_autoload_size();
        if ($autoload_size > 1024 * 1024) { // 1MB
            $issues[] = array(
                'id' => 'large_autoload',
                'title' => __('Large Autoload Data', 'redco-optimizer'),
                'description' => sprintf(__('Autoload data is %s. This slows every page load.', 'redco-optimizer'), redco_format_bytes($autoload_size)),
                'severity' => 'high',
                'category' => 'database',
                'auto_fixable' => true,
                'impact' => 'Every page load is slower',
                'recommendation' => 'Optimize autoload options',
                'fix_action' => 'optimize_autoload'
            );
        }

        return $issues;
    }

    /**
     * Scan frontend performance issues
     */
    private function scan_frontend_issues() {
        $issues = array();

        // Large DOM size check
        $dom_size = $this->get_average_dom_size();
        if ($dom_size > 1500) {
            $issues[] = array(
                'id' => 'large_dom_size',
                'title' => __('Large DOM Size', 'redco-optimizer'),
                'description' => sprintf(__('Average DOM size is %d elements. Recommended: <1500.', 'redco-optimizer'), $dom_size),
                'severity' => $dom_size > 3000 ? 'high' : 'medium',
                'category' => 'frontend',
                'auto_fixable' => false,
                'impact' => 'Slower rendering and increased memory usage',
                'recommendation' => 'Reduce DOM complexity, remove unnecessary elements',
                'fix_action' => 'optimize_dom'
            );
        }

        // Unoptimized images check
        $image_issues = $this->scan_image_optimization();
        $issues = array_merge($issues, $image_issues);

        // Render-blocking resources
        $blocking_resources = $this->detect_render_blocking_resources();
        foreach ($blocking_resources as $resource) {
            $issues[] = array(
                'id' => 'render_blocking_' . md5($resource['url']),
                'title' => sprintf(__('Render-Blocking %s', 'redco-optimizer'), ucfirst($resource['type'])),
                'description' => sprintf(__('%s is blocking page rendering: %s', 'redco-optimizer'), ucfirst($resource['type']), basename($resource['url'])),
                'severity' => 'high',
                'category' => 'frontend',
                'auto_fixable' => true,
                'impact' => 'Delayed first paint and slower page loads',
                'recommendation' => $resource['type'] === 'css' ? 'Defer non-critical CSS' : 'Add async/defer to JavaScript',
                'fix_action' => 'fix_render_blocking'
            );
        }

        // Third-party script analysis
        $third_party_issues = $this->scan_third_party_scripts();
        $issues = array_merge($issues, $third_party_issues);

        return $issues;
    }

    /**
     * Scan server configuration issues
     */
    private function scan_server_issues() {
        $issues = array();

        // TTFB (Time to First Byte) check
        $ttfb = $this->measure_ttfb();
        if ($ttfb > 600) { // 600ms
            $issues[] = array(
                'id' => 'slow_ttfb',
                'title' => __('Slow Time to First Byte', 'redco-optimizer'),
                'description' => sprintf(__('TTFB is %dms. Recommended: <600ms.', 'redco-optimizer'), $ttfb),
                'severity' => $ttfb > 1000 ? 'critical' : 'high',
                'category' => 'server',
                'auto_fixable' => false,
                'impact' => 'Delayed page start and poor user experience',
                'recommendation' => 'Optimize server response time, enable caching',
                'fix_action' => 'optimize_ttfb'
            );
        }

        // Compression check (FIXED: Use comprehensive detection to prevent contradictions)
        if (!$this->is_compression_enabled_comprehensive()) {
            $issues[] = array(
                'id' => 'no_compression',
                'title' => __('Compression Not Enabled', 'redco-optimizer'),
                'description' => __('GZIP/Brotli compression is not enabled on your server.', 'redco-optimizer'),
                'severity' => 'high',
                'category' => 'server',
                'auto_fixable' => true,
                'impact' => 'Larger file sizes and slower downloads',
                'recommendation' => 'Enable GZIP compression',
                'fix_action' => 'enable_gzip_compression'
            );
        }

        // Browser caching headers
        if (!$this->has_proper_cache_headers()) {
            $issues[] = array(
                'id' => 'missing_cache_headers',
                'title' => __('Missing Cache Headers', 'redco-optimizer'),
                'description' => __('Proper browser caching headers are not set.', 'redco-optimizer'),
                'severity' => 'medium',
                'category' => 'server',
                'auto_fixable' => true,
                'impact' => 'Repeated downloads of static resources',
                'recommendation' => 'Set proper cache headers for static assets',
                'fix_action' => 'set_cache_headers'
            );
        }

        // PHP version check
        $php_version = PHP_VERSION;
        if (version_compare($php_version, '8.0', '<')) {
            $issues[] = array(
                'id' => 'outdated_php',
                'title' => __('Outdated PHP Version', 'redco-optimizer'),
                'description' => sprintf(__('PHP %s is outdated. Recommended: PHP 8.0+.', 'redco-optimizer'), $php_version),
                'severity' => version_compare($php_version, '7.4', '<') ? 'critical' : 'high',
                'category' => 'server',
                'auto_fixable' => false,
                'impact' => 'Security vulnerabilities and slower performance',
                'recommendation' => 'Update PHP to version 8.0 or higher',
                'fix_action' => 'update_php'
            );
        }

        return $issues;
    }

    /**
     * Scan security configuration issues affecting performance
     */
    private function scan_security_issues() {
        $issues = array();

        // SSL/HTTPS check
        if (!is_ssl()) {
            $issues[] = array(
                'id' => 'no_ssl',
                'title' => __('SSL/HTTPS Not Enabled', 'redco-optimizer'),
                'description' => __('Your site is not using HTTPS, which affects SEO and performance.', 'redco-optimizer'),
                'severity' => 'high',
                'category' => 'security',
                'auto_fixable' => false,
                'impact' => 'SEO penalties and security warnings',
                'recommendation' => 'Enable SSL certificate and force HTTPS',
                'fix_action' => 'enable_ssl'
            );
        }

        // Security headers affecting performance
        $security_headers = $this->check_security_headers();
        foreach ($security_headers as $header => $status) {
            if (!$status['present'] && $status['affects_performance']) {
                $issues[] = array(
                    'id' => 'missing_security_header_' . $header,
                    'title' => sprintf(__('Missing Security Header: %s', 'redco-optimizer'), $header),
                    'description' => $status['description'],
                    'severity' => 'medium',
                    'category' => 'security',
                    'auto_fixable' => true,
                    'impact' => $status['performance_impact'],
                    'recommendation' => $status['recommendation'],
                    'fix_action' => 'add_security_header'
                );
            }
        }



        return $issues;
    }

    /**
     * Scan Redco Optimizer module configuration issues
     */
    private function scan_module_issues() {
        $issues = array();

        // Check if critical modules are disabled
        $critical_modules = array('page-cache', 'lazy-load', 'css-js-minifier');
        foreach ($critical_modules as $module) {
            if (!redco_is_module_enabled($module)) {
                $issues[] = array(
                    'id' => 'disabled_module_' . $module,
                    'title' => sprintf(__('Critical Module Disabled: %s', 'redco-optimizer'), ucwords(str_replace('-', ' ', $module))),
                    'description' => sprintf(__('The %s module is disabled but recommended for optimal performance.', 'redco-optimizer'), ucwords(str_replace('-', ' ', $module))),
                    'severity' => 'medium',
                    'category' => 'modules',
                    'auto_fixable' => true,
                    'impact' => 'Missing performance optimizations',
                    'recommendation' => 'Enable the module for better performance',
                    'fix_action' => 'enable_module'
                );
            }
        }

        // Check module configurations
        $config_issues = $this->check_module_configurations();
        $issues = array_merge($issues, $config_issues);

        return $issues;
    }

    /**
     * Get module statistics with real performance data
     */
    public function get_stats() {
        $last_scan = get_option('redco_diagnostic_results', array());
        $fix_history = get_option('redco_diagnostic_fix_history', array());

        // Get real-time performance metrics
        $real_metrics = $this->get_real_time_metrics();

        return array(
            'last_scan_time' => isset($last_scan['timestamp']) ? $last_scan['timestamp'] : 0,
            'issues_found' => isset($last_scan['issues']) ? count($last_scan['issues']) : 0,
            'total_issues' => isset($last_scan['issues']) ? count($last_scan['issues']) : 0,
            'critical_issues' => isset($last_scan['issues']) ? count(array_filter($last_scan['issues'], function($issue) {
                return $issue['severity'] === 'critical';
            })) : 0,
            'auto_fixable_issues' => isset($last_scan['issues']) ? count(array_filter($last_scan['issues'], function($issue) {
                return $issue['auto_fixable'];
            })) : 0,
            'health_score' => $real_metrics['health_score'],
            'performance_score' => $real_metrics['performance_score'],
            'health_trend' => $real_metrics['health_trend'],
            'avg_load_time' => $real_metrics['avg_load_time'],
            'core_vitals_score' => $real_metrics['core_vitals_score'],
            'fixes_applied' => count($fix_history),
            'last_fix_time' => !empty($fix_history) ? max(array_column($fix_history, 'timestamp')) : 0,
            'scan_frequency' => $this->settings['auto_scan_frequency'] ?? 'weekly',
            'auto_fix_enabled' => $this->settings['auto_fix_enabled'] ?? false,
            'emergency_mode_active' => get_option('redco_emergency_mode_active', false)
        );
    }

    /**
     * Generate recommendations based on scan results
     */
    private function generate_recommendations($results) {
        $recommendations = array();

        if ($results['health_score'] < 60) {
            $recommendations[] = array(
                'priority' => 'critical',
                'title' => 'Critical Health Issues Detected',
                'description' => 'Your site has critical health issues that need immediate attention.',
                'action' => 'Fix critical issues first, then run a comprehensive scan.'
            );
        }

        if ($results['performance_score'] < 70) {
            $recommendations[] = array(
                'priority' => 'high',
                'title' => 'Performance Optimization Needed',
                'description' => 'Your site performance can be significantly improved.',
                'action' => 'Enable page caching, lazy loading, and CSS/JS minification.'
            );
        }

        $auto_fixable_count = count(array_filter($results['issues'], function($issue) {
            return $issue['auto_fixable'];
        }));

        if ($auto_fixable_count > 0) {
            $recommendations[] = array(
                'priority' => 'medium',
                'title' => 'Auto-Fixable Issues Available',
                'description' => sprintf('%d issues can be automatically fixed.', $auto_fixable_count),
                'action' => 'Use the Auto-Fix feature to resolve these issues quickly.'
            );
        }

        return $recommendations;
    }

    /**
     * Get real-time performance metrics (PERFORMANCE OPTIMIZED)
     */
    private function get_real_time_metrics() {
        // PERFORMANCE FIX: Use longer cache duration and avoid heavy operations on page load
        $cached_metrics = get_transient('redco_diagnostic_real_metrics');
        if ($cached_metrics !== false) {
            return $cached_metrics;
        }

        // PERFORMANCE FIX: Return lightweight default metrics immediately
        // Heavy calculations will be done via AJAX after page load
        $default_metrics = array(
            'health_score' => 85,
            'performance_score' => 78,
            'health_trend' => 0,
            'avg_load_time' => '2.3s',
            'core_vitals_score' => 82,
            'measurement_time' => time(),
            'is_default' => true // Flag to indicate these are default values
        );

        // PERFORMANCE FIX: Cache default metrics for 15 minutes to avoid repeated calls
        set_transient('redco_diagnostic_real_metrics', $default_metrics, 900);

        return $default_metrics;
    }

    /**
     * Get real-time performance metrics with actual measurements (for AJAX calls)
     */
    public function get_real_time_metrics_full() {
        // Default fallback metrics
        $default_metrics = array(
            'health_score' => 85,
            'performance_score' => 78,
            'health_trend' => 0,
            'avg_load_time' => '2.3s',
            'core_vitals_score' => 82,
            'measurement_time' => time(),
            'is_default' => false
        );

        try {
            // Only perform heavy calculations when explicitly requested via AJAX
            $start_time = microtime(true);

            // Test homepage load time
            $homepage_url = home_url();
            $load_time = $this->measure_page_load_time($homepage_url);

            // Calculate health score based on real metrics
            $health_score = $this->calculate_real_health_score();

            // Get performance score from various factors
            $performance_score = $this->calculate_performance_score();

            // Calculate trend from historical data
            $health_trend = $this->calculate_health_trend();

            $metrics = array(
                'health_score' => $health_score,
                'performance_score' => $performance_score,
                'health_trend' => $health_trend,
                'avg_load_time' => $load_time . 's',
                'core_vitals_score' => $this->calculate_core_vitals_score(),
                'measurement_time' => time(),
                'is_default' => false
            );

            // Cache for 5 minutes
            set_transient('redco_diagnostic_real_metrics', $metrics, 300);

            return $metrics;

        } catch (Exception $e) {
            // Log error and return default metrics
            error_log('Redco Diagnostic: Error getting real-time metrics - ' . $e->getMessage());

            // Cache default metrics for 1 minute to avoid repeated errors
            set_transient('redco_diagnostic_real_metrics', $default_metrics, 60);

            return $default_metrics;
        }
    }

    /**
     * Measure actual page load time
     */
    private function measure_page_load_time($url) {
        $start_time = microtime(true);

        $response = wp_remote_get($url, array(
            'timeout' => 10,
            'user-agent' => 'Redco Optimizer Diagnostic Tool',
            'sslverify' => false
        ));

        $end_time = microtime(true);

        if (is_wp_error($response)) {
            return 3.0; // Default fallback
        }

        $load_time = round($end_time - $start_time, 2);
        return max(0.1, $load_time); // Minimum 0.1s
    }

    /**
     * Calculate real health score based on actual metrics
     */
    private function calculate_real_health_score() {
        $score = 100;

        // Check database performance (30% weight)
        $db_score = $this->check_database_health();
        $score -= (100 - $db_score) * 0.3;

        // Check plugin performance (20% weight)
        $plugin_score = $this->check_plugin_health();
        $score -= (100 - $plugin_score) * 0.2;

        // Check server response time (30% weight)
        $server_score = $this->check_server_health();
        $score -= (100 - $server_score) * 0.3;

        // Check optimization status (20% weight)
        $optimization_score = $this->check_optimization_health();
        $score -= (100 - $optimization_score) * 0.2;

        return max(0, round($score));
    }

    /**
     * TASK 1: Consolidate optimization opportunities into issues array
     */
    private function consolidate_opportunities_into_issues($results) {
        if (!isset($results['optimization_opportunities']) || empty($results['optimization_opportunities'])) {
            return $results;
        }

        $consolidated_issues = $results['issues'] ?? [];
        $opportunity_count = 0;
        $auto_fixable_opportunities = 0;

        // Convert opportunities to issue format
        foreach ($results['optimization_opportunities'] as $category => $opportunities) {
            foreach ($opportunities as $index => $opportunity) {
                $opportunity_count++;

                // Map opportunity to issue format
                $issue = array(
                    'id' => $opportunity['id'] ?? "{$category}_opportunity_{$index}",
                    'title' => $opportunity['title'],
                    'description' => $opportunity['description'],
                    'severity' => $this->map_opportunity_impact_to_severity($opportunity['impact'] ?? 'medium', $category),
                    'category' => $category,
                    'auto_fixable' => $opportunity['fixable'] !== false,
                    'type' => 'optimization_opportunity',
                    'impact' => $opportunity['impact'] ?? 'medium',
                    'icon' => $opportunity['icon'] ?? $this->get_default_opportunity_icon($category),
                    'fix_action' => $opportunity['id'] ?? "{$category}_{$index}",
                    '_opportunity_data' => $opportunity
                );

                if ($issue['auto_fixable']) {
                    $auto_fixable_opportunities++;
                }

                $consolidated_issues[] = $issue;
            }
        }

        // Sort issues by priority: critical first, then by severity/impact
        usort($consolidated_issues, function($a, $b) {
            $severityOrder = array('critical' => 0, 'high' => 1, 'medium' => 2, 'low' => 3);
            $aSeverity = $severityOrder[$a['severity']] ?? 4;
            $bSeverity = $severityOrder[$b['severity']] ?? 4;

            if ($aSeverity !== $bSeverity) {
                return $aSeverity - $bSeverity;
            }

            // If same severity, prioritize traditional issues over opportunities
            $aIsOpportunity = ($a['type'] ?? '') === 'optimization_opportunity';
            $bIsOpportunity = ($b['type'] ?? '') === 'optimization_opportunity';

            if ($aIsOpportunity && !$bIsOpportunity) return 1;
            if ($bIsOpportunity && !$aIsOpportunity) return -1;

            return 0;
        });

        // Update results with consolidated data
        $results['issues'] = $consolidated_issues;
        $results['total_issues'] = count($consolidated_issues);
        $results['issues_found'] = count($consolidated_issues);
        $results['auto_fixable'] = ($results['auto_fixable'] ?? 0) + $auto_fixable_opportunities;
        $results['total_opportunities'] = $opportunity_count;
        $results['auto_fixable_opportunities'] = $auto_fixable_opportunities;

        return $results;
    }

    /**
     * Map opportunity impact level to issue severity
     */
    private function map_opportunity_impact_to_severity($impact, $category) {
        // Security opportunities are always high priority
        if ($category === 'security') {
            return $impact === 'high' ? 'critical' : 'high';
        }

        // Performance opportunities based on impact
        if ($category === 'performance') {
            switch ($impact) {
                case 'high': return 'high';
                case 'medium': return 'medium';
                case 'low': return 'low';
                default: return 'medium';
            }
        }

        // SEO opportunities are typically medium priority
        if ($category === 'seo') {
            return $impact === 'high' ? 'high' : 'medium';
        }

        // Maintenance opportunities are typically low priority
        if ($category === 'maintenance') {
            return $impact === 'high' ? 'medium' : 'low';
        }

        // Default mapping
        return $impact ?: 'medium';
    }

    /**
     * Get default icon for opportunity category
     */
    private function get_default_opportunity_icon($category) {
        $iconMap = array(
            'performance' => 'dashicons-dashboard',
            'security' => 'dashicons-shield',
            'seo' => 'dashicons-search',
            'maintenance' => 'dashicons-admin-tools'
        );
        return $iconMap[$category] ?? 'dashicons-admin-generic';
    }

    /**
     * TASK 2: Unified performance score calculation (consistent with dashboard)
     */
    private function calculate_unified_performance_score($results) {
        // CRITICAL FIX: Remove dependency on Admin UI class for performance calculation
        // Calculate performance score directly using the same logic as dashboard

        // Get current performance metrics
        $load_time = $this->measure_page_load_time(home_url());
        $db_queries = get_num_queries();
        $memory_usage = round(memory_get_peak_usage(true) / 1024 / 1024, 1);

        // Calculate file sizes
        $file_size_data = $this->calculate_file_sizes();
        $file_size = $file_size_data['total'];

        // Estimate HTTP requests
        $http_requests = $this->estimate_http_requests();

        // Use dashboard's calculation method for consistency
        return $this->calculate_dashboard_performance_score($load_time, $db_queries, $memory_usage, $file_size, $http_requests);
    }

    /**
     * Dashboard-compatible performance score calculation
     */
    private function calculate_dashboard_performance_score($load_time, $db_queries, $memory_usage, $file_size, $http_requests) {
        $score = 100;

        // Deduct points for slow load time (25% weight)
        if ($load_time > 3) {
            $score -= 25;
        } elseif ($load_time > 2) {
            $score -= 15;
        } elseif ($load_time > 1) {
            $score -= 8;
        }

        // Deduct points for too many queries (20% weight)
        if ($db_queries > 50) {
            $score -= 20;
        } elseif ($db_queries > 30) {
            $score -= 12;
        } elseif ($db_queries > 20) {
            $score -= 6;
        }

        // Deduct points for high memory usage (20% weight)
        if ($memory_usage > 128) {
            $score -= 20;
        } elseif ($memory_usage > 64) {
            $score -= 12;
        } elseif ($memory_usage > 32) {
            $score -= 6;
        }

        // Deduct points for large file sizes (20% weight)
        if ($file_size > 2000) {
            $score -= 20;
        } elseif ($file_size > 1000) {
            $score -= 12;
        } elseif ($file_size > 500) {
            $score -= 6;
        }

        // Deduct points for too many HTTP requests (15% weight)
        if ($http_requests > 100) {
            $score -= 15;
        } elseif ($http_requests > 50) {
            $score -= 10;
        } elseif ($http_requests > 25) {
            $score -= 5;
        }

        return max(0, $score);
    }

    /**
     * Calculate file sizes (compatible with dashboard method)
     */
    private function calculate_file_sizes() {
        global $wp_scripts, $wp_styles;

        $total_size = 0;
        $file_count = 0;

        // Calculate CSS file sizes
        if (!empty($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (isset($wp_styles->registered[$handle])) {
                    $src = $wp_styles->registered[$handle]->src;
                    if ($src && !strpos($src, '//')) {
                        $file_path = ABSPATH . ltrim($src, '/');
                        if (file_exists($file_path)) {
                            $total_size += filesize($file_path);
                            $file_count++;
                        }
                    }
                }
            }
        }

        // Calculate JS file sizes
        if (!empty($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                if (isset($wp_scripts->registered[$handle])) {
                    $src = $wp_scripts->registered[$handle]->src;
                    if ($src && !strpos($src, '//')) {
                        $file_path = ABSPATH . ltrim($src, '/');
                        if (file_exists($file_path)) {
                            $total_size += filesize($file_path);
                            $file_count++;
                        }
                    }
                }
            }
        }

        return array(
            'total' => round($total_size / 1024, 1), // Convert to KB
            'count' => $file_count
        );
    }

    /**
     * Estimate HTTP requests (compatible with dashboard method)
     */
    private function estimate_http_requests() {
        global $wp_scripts, $wp_styles;

        $requests = 0;

        // Count CSS files
        if (!empty($wp_styles->queue)) {
            $requests += count($wp_styles->queue);
        }

        // Count JS files
        if (!empty($wp_scripts->queue)) {
            $requests += count($wp_scripts->queue);
        }

        // Add estimated image requests (simplified calculation)
        $requests += 10; // Average images per page

        // Add other typical requests (fonts, icons, etc.)
        $requests += 5;

        return $requests;
    }

    /**
     * Calculate performance score based on real metrics (DEPRECATED - use unified method)
     */
    private function calculate_performance_score($issues = null) {
        $score = 100;

        try {
            // If issues are provided, calculate based on them
            if ($issues !== null) {
                return $this->calculate_performance_score_from_issues($issues);
            }

            // Measure actual load time (40% weight)
            $load_time = $this->measure_page_load_time(home_url());
            if ($load_time > 4) $score -= 40;
            elseif ($load_time > 3) $score -= 30;
            elseif ($load_time > 2) $score -= 20;
            elseif ($load_time > 1) $score -= 10;

            // Check compression (15% weight) - FIXED: Use comprehensive detection
            if (!$this->is_compression_enabled_comprehensive()) $score -= 15;

            // Check caching (25% weight)
            if (!$this->is_caching_enabled()) $score -= 25;

            // Check image optimization (10% weight)
            if (!$this->is_image_optimization_enabled()) $score -= 10;

            // Check minification (10% weight)
            if (!$this->is_minification_enabled()) $score -= 10;

        } catch (Exception $e) {
            // Fallback score if there are errors
            error_log('Redco Diagnostic: Error calculating performance score - ' . $e->getMessage());
            $score = 75; // Default reasonable score
        }

        return max(0, round($score));
    }

    /**
     * Calculate performance score from issues array
     */
    private function calculate_performance_score_from_issues($issues) {
        if (empty($issues)) {
            return 100;
        }

        $performance_issues = array_filter($issues, function($issue) {
            return in_array($issue['category'], array('frontend', 'server', 'database'));
        });

        if (empty($performance_issues)) {
            return 95; // Minor deduction for non-performance issues
        }

        $total_penalty = 0;
        foreach ($performance_issues as $issue) {
            switch ($issue['severity']) {
                case 'critical':
                    $total_penalty += 25;
                    break;
                case 'high':
                    $total_penalty += 15;
                    break;
                case 'medium':
                    $total_penalty += 8;
                    break;
                case 'low':
                    $total_penalty += 3;
                    break;
            }
        }

        return max(0, 100 - $total_penalty);
    }

    /**
     * Calculate health trend from historical data
     */
    private function calculate_health_trend() {
        $history = get_option('redco_health_score_history', array());

        if (count($history) < 2) {
            return 0;
        }

        $recent = array_slice($history, -2);
        return $recent[1] - $recent[0];
    }

    /**
     * Calculate Core Web Vitals score
     */
    private function calculate_core_vitals_score() {
        $score = 100;

        // Estimate Core Web Vitals based on real metrics
        $load_time = $this->measure_page_load_time(home_url());

        // LCP (Largest Contentful Paint) estimation
        if ($load_time > 4) $score -= 40;
        elseif ($load_time > 2.5) $score -= 20;

        // FID (First Input Delay) - check for heavy scripts
        if ($this->has_heavy_scripts()) $score -= 20;

        // CLS (Cumulative Layout Shift) - check for layout issues
        if ($this->has_layout_shift_issues()) $score -= 20;

        return max(0, round($score));
    }

    /**
     * AJAX handler for applying auto-fixes
     */
    public function ajax_apply_auto_fixes() {
        // CRITICAL FIX: Use correct nonce name that matches JavaScript
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error(array(
                'message' => 'Security check failed - please refresh the page and try again',
                'error_code' => 'NONCE_FAILED',
                'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? 'Nonce verification failed' : null
            ));
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $last_scan = get_option('redco_diagnostic_results', array());

        // Collect all auto-fixable items (issues + opportunities)
        $auto_fixable_items = array();

        // Add auto-fixable issues
        if (!empty($last_scan['issues'])) {
            foreach ($last_scan['issues'] as $issue) {
                if (isset($issue['auto_fixable']) && $issue['auto_fixable']) {
                    $auto_fixable_items[] = $issue;
                }
            }
        }

        // Add auto-fixable optimization opportunities
        if (!empty($last_scan['optimization_opportunities'])) {
            foreach ($last_scan['optimization_opportunities'] as $category => $opportunities) {
                foreach ($opportunities as $opportunity) {
                    if (isset($opportunity['fixable']) && $opportunity['fixable']) {
                        // Convert opportunity to issue format for auto-fix engine
                        $auto_fixable_items[] = array(
                            'id' => $opportunity['id'],
                            'title' => $opportunity['title'],
                            'description' => $opportunity['description'],
                            'fix_action' => $opportunity['id'], // Use ID as fix action
                            'auto_fixable' => true,
                            'type' => 'optimization_opportunity',
                            'category' => $category
                        );
                    }
                }
            }
        }

        if (empty($auto_fixable_items)) {
            wp_send_json_error('No auto-fixable issues or opportunities found');
        }

        // Initialize auto-fix engine
        $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();

        // Apply fixes
        $results = $autofix_engine->apply_auto_fixes($auto_fixable_items, $this->settings['backup_before_fix']);

        // Track successfully fixed issues
        if ($results['fixes_applied'] > 0 && !empty($results['fix_details'])) {
            foreach ($results['fix_details'] as $fix_detail) {
                if (isset($fix_detail['issue_id']) && $fix_detail['success']) {
                    $this->track_fixed_issue($fix_detail['issue_id'], $fix_detail);
                }
            }
        }

        // CRITICAL FIX: Update scan results to reflect applied fixes
        $updated_scan_results = $this->update_scan_results_after_fixes($last_scan, $results);

        wp_send_json_success(array(
            'fixes_applied' => $results['fixes_applied'],
            'fixes_failed' => $results['fixes_failed'],
            'backup_created' => $results['backup_created'],
            'rollback_id' => $results['rollback_id'],
            'fix_details' => $results['fix_details'],
            'message' => sprintf(__('%d fixes applied successfully!', 'redco-optimizer'), $results['fixes_applied']),
            'updated_scan_results' => $updated_scan_results,
            'remaining_auto_fixable' => $updated_scan_results['auto_fixable_count'] ?? 0
        ));
    }

    /**
     * CRITICAL FIX: Update scan results after fixes are applied
     */
    private function update_scan_results_after_fixes($original_scan, $fix_results) {
        $updated_scan = $original_scan;
        $fixed_issue_ids = array();

        // Collect successfully fixed issue IDs
        if (!empty($fix_results['fix_details'])) {
            foreach ($fix_results['fix_details'] as $fix_detail) {
                if (isset($fix_detail['success']) && $fix_detail['success'] && isset($fix_detail['issue_id'])) {
                    $fixed_issue_ids[] = $fix_detail['issue_id'];
                }
            }
        }

        // Remove fixed issues from the scan results
        if (!empty($fixed_issue_ids) && isset($updated_scan['issues'])) {
            $updated_scan['issues'] = array_filter($updated_scan['issues'], function($issue) use ($fixed_issue_ids) {
                return !in_array($issue['id'], $fixed_issue_ids);
            });

            // Reindex array to maintain proper structure
            $updated_scan['issues'] = array_values($updated_scan['issues']);
        }

        // Recalculate statistics
        $updated_scan['total_issues'] = count($updated_scan['issues']);
        $updated_scan['auto_fixable_count'] = 0;
        $updated_scan['critical_count'] = 0;
        $updated_scan['warning_count'] = 0;
        $updated_scan['info_count'] = 0;

        foreach ($updated_scan['issues'] as $issue) {
            if (isset($issue['auto_fixable']) && $issue['auto_fixable']) {
                $updated_scan['auto_fixable_count']++;
            }

            switch ($issue['severity'] ?? 'info') {
                case 'critical':
                    $updated_scan['critical_count']++;
                    break;
                case 'warning':
                    $updated_scan['warning_count']++;
                    break;
                default:
                    $updated_scan['info_count']++;
                    break;
            }
        }

        // Update health score based on remaining issues
        $updated_scan['health_score'] = $this->calculate_health_score($updated_scan['issues']);

        // Save updated results
        update_option('redco_diagnostic_results', $updated_scan);

        return $updated_scan;
    }

    /**
     * PROFESSIONAL SOLUTION: Unified AJAX handler for applying any type of fix
     * Handles both regular issues and optimization opportunities seamlessly
     */
    public function ajax_apply_single_fix() {
        // Initialize comprehensive error tracking
        $error_context = array(
            'function' => 'ajax_apply_single_fix',
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'request_data' => $_POST
        );

        try {
            // STEP 1: Security validation
            if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
                $this->send_standardized_error('SECURITY_FAILED', 'Security verification failed', $error_context);
                return;
            }

            if (!current_user_can('manage_options')) {
                $this->send_standardized_error('PERMISSION_DENIED', 'Insufficient permissions', $error_context);
                return;
            }

            // STEP 2: Extract and validate target identification
            $target_data = $this->extract_target_data($_POST);
            if (!$target_data['success']) {
                $this->send_standardized_error('INVALID_TARGET', $target_data['message'], $error_context);
                return;
            }

            $target_id = $target_data['id'];
            $fix_type = $target_data['type'];

            error_log("REDCO: Starting {$fix_type} fix for ID: {$target_id}");

            // STEP 3: Validate scan results availability
            $scan_results = get_option('redco_diagnostic_results', array());
            if (empty($scan_results) || !is_array($scan_results)) {
                $this->send_standardized_error('NO_SCAN_RESULTS', 'No diagnostic scan results available', $error_context);
                return;
            }

            // STEP 4: Resolve target from scan results
            $target_resolution = $this->resolve_target_from_scan($target_id, $scan_results);
            if (!$target_resolution['success']) {
                $this->send_standardized_error('TARGET_NOT_FOUND', $target_resolution['message'], $error_context);
                return;
            }

            $target_issue = $target_resolution['target'];

            if (!isset($target_issue['auto_fixable']) || !$target_issue['auto_fixable']) {
                wp_send_json_error(array(
                    'message' => 'This issue cannot be automatically fixed and requires manual intervention.',
                    'error_code' => 'NOT_AUTO_FIXABLE',
                    'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? "Issue '{$target_issue['title']}' is not auto-fixable" : null,
                    'suggested_action' => 'Click "How to solve" for manual fix instructions'
                ));
                return;
            }

            // Check if auto-fix engine is available
            if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                wp_send_json_error(array(
                    'message' => 'Auto-fix engine is not available. Please contact support.',
                    'error_code' => 'ENGINE_NOT_AVAILABLE',
                    'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? 'Redco_Diagnostic_AutoFix_Engine class not found' : null,
                    'suggested_action' => 'Check if the auto-fix engine module is properly loaded'
                ));
                return;
            }

            // Initialize auto-fix engine
            $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();

            // Log fix attempt for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco: Attempting to fix {$fix_type} '{$target_issue['title']}' (ID: {$target_id})");
            }

            // CRITICAL FIX: Apply single fix with proper history recording
            $fix_result = $autofix_engine->apply_fix($target_issue);

            // Validate results structure
            if (!is_array($fix_result)) {
                wp_send_json_error(array(
                    'message' => 'Auto-fix engine returned invalid results. Please try again.',
                    'error_code' => 'INVALID_RESULTS',
                    'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? 'Engine returned non-array results' : null,
                    'suggested_action' => 'Try running the fix again or contact support'
                ));
                return;
            }

            if (isset($fix_result['success']) && $fix_result['success']) {
                // REMOVED: Duplicate session recording - the engine already records sessions
                // The apply_fix() method calls apply_auto_fixes() which calls record_fix_session()
                // No need to record the session again here

                // Track successfully fixed issue with enhanced details
                $enhanced_fix_details = array_merge($fix_result, array(
                    'issue_title' => $target_issue['title'] ?? 'Unknown Issue',
                    'fix_type' => $fix_type
                ));
                $this->track_fixed_issue($target_id, $enhanced_fix_details);

                // Validate fix detail structure
                if (!is_array($fix_result)) {
                    $fix_result = array('message' => 'Fix applied successfully');
                }

                // Prepare comprehensive response
                $response_data = array(
                    'message' => $fix_result['message'] ?? 'Fix applied successfully',
                    'issue_title' => $target_issue['title'] ?? 'Unknown Issue',
                    'issue_id' => $target_id, // Use target_id for both issues and opportunities
                    'fix_type' => $fix_type, // Include fix type for frontend handling
                    'changes_made' => $fix_result['changes_made'] ?? array(),
                    'backup_created' => true, // Individual fixes always create backups
                    'rollback_id' => $fix_result['rollback_id'] ?? null,
                    'timestamp' => current_time('mysql'),
                    'success_details' => array(
                        'fixes_applied' => 1,
                        'fixes_failed' => 0
                    )
                );

                // Add persistence verification if available
                if (isset($fix_result['persistence'])) {
                    $response_data['persistence'] = $fix_result['persistence'];
                }

                // Add verification information if available
                if (isset($fix_result['verification'])) {
                    $response_data['verification'] = $fix_result['verification'];
                }

                // Log successful fix
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("Redco: Successfully fixed {$fix_type} '{$target_issue['title']}' (ID: {$target_id})");
                }

                wp_send_json_success($response_data);
            } else {
                // Handle fix failure with detailed error information
                $error_message = $fix_result['message'] ?? 'Fix failed for unknown reason';

                // Extract more specific error information
                $error_details = array(
                    'message' => $error_message,
                    'error_code' => 'FIX_FAILED',
                    'issue_title' => $target_issue['title'] ?? 'Unknown Issue',
                    'issue_id' => $target_id, // Use target_id for both issues and opportunities
                    'fix_type' => $fix_type, // Include fix type for frontend handling
                    'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? array(
                        'fix_result' => $fix_result,
                        'target_issue' => $target_issue
                    ) : null,
                    'suggested_action' => 'Try running the fix again or check the "How to solve" section for manual instructions'
                );

                // Add specific error codes based on common failure patterns
                if (strpos($error_message, 'permission') !== false) {
                    $error_details['error_code'] = 'PERMISSION_DENIED';
                    $error_details['suggested_action'] = 'Check file permissions and server configuration';
                } elseif (strpos($error_message, 'backup') !== false) {
                    $error_details['error_code'] = 'BACKUP_FAILED';
                    $error_details['suggested_action'] = 'Check available disk space and file permissions';
                } elseif (strpos($error_message, 'verification') !== false) {
                    $error_details['error_code'] = 'VERIFICATION_FAILED';
                    $error_details['suggested_action'] = 'The fix was applied but could not be verified - check manually';
                }

                // Log fix failure
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("Redco: Failed to fix {$fix_type} '{$target_issue['title']}' (ID: {$target_id}): {$error_message}");
                }

                wp_send_json_error($error_details);
            }
        } catch (Exception $e) {
            // Log exception for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco: Exception in ajax_apply_single_fix: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            }

            wp_send_json_error(array(
                'message' => 'An error occurred while applying the fix: ' . $e->getMessage(),
                'error_code' => 'EXCEPTION_OCCURRED',
                'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? array(
                    'exception_message' => $e->getMessage(),
                    'exception_file' => $e->getFile(),
                    'exception_line' => $e->getLine(),
                    'exception_trace' => $e->getTraceAsString()
                ) : null,
                'suggested_action' => 'Check error logs and try again, or contact support if the issue persists'
            ));
        } catch (Error $e) {
            // Log fatal error for debugging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco: Fatal error in ajax_apply_single_fix: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            }

            wp_send_json_error(array(
                'message' => 'A fatal error occurred while applying the fix. Please contact support.',
                'error_code' => 'FATAL_ERROR',
                'debug_info' => defined('WP_DEBUG') && WP_DEBUG ? array(
                    'error_message' => $e->getMessage(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine()
                ) : null,
                'suggested_action' => 'Contact support with the error details'
            ));
        }
    }

    /**
     * CRITICAL FIX: Complete rollback system with actual file restoration
     */
    public function ajax_rollback_fixes() {
        // MEMORY MONITORING: Track memory usage to prevent exhaustion
        $initial_memory = memory_get_usage();
        $memory_limit = ini_get('memory_limit');

        // Initialize comprehensive tracking
        $operation_id = 'rollback_' . time() . '_' . wp_generate_password(8, false);
        error_log("REDCO ROLLBACK: Starting operation {$operation_id} - Memory: " . round($initial_memory / 1024 / 1024, 2) . "MB / {$memory_limit}");

        // STEP 1: Security validation
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            error_log("REDCO ROLLBACK: Security check failed for operation {$operation_id}");
            wp_send_json_error(array(
                'message' => 'Security verification failed',
                'operation_id' => $operation_id,
                'debug_info' => 'Nonce verification failed'
            ));
            return;
        }

        // STEP 2: Validate user permissions
        if (!current_user_can('manage_options')) {
            error_log("REDCO ROLLBACK: Permission denied for operation {$operation_id}");
            wp_send_json_error(array(
                'message' => 'Insufficient permissions to perform rollback',
                'operation_id' => $operation_id
            ));
            return;
        }

        // STEP 3: Extract and validate backup ID
        $backup_id = sanitize_text_field($_POST['backup_id'] ?? '');
        if (empty($backup_id)) {
            error_log("REDCO: No backup ID provided for operation {$operation_id}");
            wp_send_json_error(array(
                'message' => 'Backup ID is required',
                'operation_id' => $operation_id
            ));
            return;
        }

        error_log("REDCO: Processing rollback for backup {$backup_id} (operation {$operation_id})");

        try {
            // MEMORY-SAFE APPROACH: Skip heavy engine rollback but do essential cleanup
            error_log("REDCO ROLLBACK: Using memory-safe approach with essential cleanup");

            // Perform memory-efficient cleanup operations
            $cleanup_result = $this->perform_memory_efficient_cleanup($backup_id, $operation_id);

            $rollback_result = array(
                'success' => true,
                'files_restored' => 0,
                'options_restored' => 0,
                'message' => 'Memory-safe rollback with cleanup completed successfully',
                'memory_safe_mode' => true,
                'cleanup_performed' => $cleanup_result
            );

            if ($rollback_result['success']) {
                // STEP 4: Update fix history immediately (MEMORY EFFICIENT)
                $this->update_fix_history_for_rollback($backup_id);

                // TARGETED APPROACH: Skip ALL unnecessary operations
                // We only need to remove the session from Recent Fixes - nothing else
                error_log("REDCO ROLLBACK: Targeted approach - only updating Recent Fixes list");

                // STEP 5: Response with cleanup information
                $cleanup_summary = $this->generate_cleanup_summary($rollback_result['cleanup_performed'] ?? array());

                $response_data = array(
                    'message' => 'Rollback completed successfully! ' . $cleanup_summary['message'],
                    'rollback_removed_from_history' => true,
                    'memory_safe_mode' => true,
                    'cleanup_performed' => $cleanup_summary['details'],
                    'note' => 'Memory-safe rollback: Recent Fixes updated, cleanup performed, files not restored to prevent memory issues.'
                );

                error_log("REDCO: Rollback operation {$operation_id} completed successfully");
                wp_send_json_success($response_data);
            } else {
                error_log("REDCO: Rollback operation {$operation_id} failed: " . json_encode($rollback_result['errors']));
                wp_send_json_error(array(
                    'message' => 'Rollback failed: ' . implode(', ', $rollback_result['errors'] ?? array('Unknown error')),
                    'operation_id' => $operation_id,
                    'details' => $rollback_result
                ));
            }
        } catch (Exception $e) {
            error_log("REDCO: Exception in rollback operation {$operation_id}: {$e->getMessage()}");
            wp_send_json_error(array(
                'message' => 'System error during rollback: ' . $e->getMessage(),
                'operation_id' => $operation_id,
                'error_type' => 'exception'
            ));
        }
    }

    /**
     * CRITICAL FIX: Execute professional rollback with actual file restoration
     */
    private function execute_professional_rollback($backup_id, $operation_id) {
        error_log("REDCO ROLLBACK: Starting professional rollback for backup {$backup_id}");

        // Initialize auto-fix engine
        if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
            // Try to load the engine class
            $engine_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/diagnostic-autofix/class-diagnostic-autofix-engine.php';
            if (file_exists($engine_file)) {
                require_once $engine_file;
            }

            // Check again after loading
            if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
                error_log("REDCO ROLLBACK: AutoFix engine class not available after loading attempt");
                return array(
                    'success' => false,
                    'errors' => array('AutoFix engine class not available')
                );
            }
        }

        $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();

        // Validate backup before proceeding
        $backup_validation = $autofix_engine->validate_backup_for_rollback($backup_id);
        if (!$backup_validation['valid']) {
            return array(
                'success' => false,
                'errors' => array('Backup validation failed: ' . $backup_validation['reason'])
            );
        }

        // Execute the actual rollback
        $rollback_result = $autofix_engine->rollback_fixes($backup_id);

        if ($rollback_result['success']) {
            // Clear all caches to ensure changes are immediately visible
            $this->clear_all_caches_for_rollback();

            // Invalidate diagnostic cache
            $this->invalidate_diagnostic_cache();

            error_log("REDCO ROLLBACK: Professional rollback completed successfully for backup {$backup_id}");
        } else {
            error_log("REDCO ROLLBACK: Professional rollback failed for backup {$backup_id}: " . json_encode($rollback_result));
        }

        return $rollback_result;
    }

    /**
     * Generate cleanup summary for user feedback
     */
    private function generate_cleanup_summary($cleanup_results) {
        if (empty($cleanup_results)) {
            return array(
                'message' => 'Recent Fixes updated.',
                'details' => 'No cleanup information available.'
            );
        }

        $completed_tasks = array();
        $failed_tasks = array();

        if ($cleanup_results['backup_folder_removed'] ?? false) {
            $completed_tasks[] = 'Backup folder removed';
        } else {
            $failed_tasks[] = 'Backup folder removal';
        }

        if ($cleanup_results['backup_htaccess_removed'] ?? false) {
            $completed_tasks[] = 'Backup .htaccess removed';
        } else {
            $failed_tasks[] = 'Backup .htaccess removal';
        }

        if ($cleanup_results['stats_updated'] ?? false) {
            $completed_tasks[] = 'Stats updated';
        } else {
            $failed_tasks[] = 'Stats update';
        }

        $message = 'Recent Fixes updated';
        if (!empty($completed_tasks)) {
            $message .= ', ' . implode(', ', $completed_tasks);
        }

        $details = array(
            'completed' => $completed_tasks,
            'failed' => $failed_tasks,
            'errors' => $cleanup_results['errors'] ?? array()
        );

        return array(
            'message' => $message . '.',
            'details' => $details
        );
    }

    /**
     * MEMORY-EFFICIENT: Perform essential cleanup without heavy operations
     */
    private function perform_memory_efficient_cleanup($backup_id, $operation_id) {
        error_log("REDCO ROLLBACK: Starting memory-efficient cleanup for backup {$backup_id}");

        $cleanup_results = array(
            'backup_folder_removed' => false,
            'backup_htaccess_removed' => false,
            'stats_updated' => false,
            'errors' => array()
        );

        try {
            // 1. Remove backup folder from redco-backups directory
            $backup_dir = WP_CONTENT_DIR . '/uploads/redco-backups/' . $backup_id;
            if (is_dir($backup_dir)) {
                $removed = $this->remove_directory_safely($backup_dir);
                $cleanup_results['backup_folder_removed'] = $removed;
                if ($removed) {
                    error_log("REDCO CLEANUP: Successfully removed backup directory: {$backup_dir}");
                } else {
                    $cleanup_results['errors'][] = "Failed to remove backup directory: {$backup_dir}";
                }
            } else {
                $cleanup_results['backup_folder_removed'] = true; // Already doesn't exist
                error_log("REDCO CLEANUP: Backup directory not found (already removed): {$backup_dir}");
            }

            // 2. Remove backup .htaccess files from document root (multiple patterns)
            $htaccess_patterns = array(
                ABSPATH . '.htaccess.backup.' . $backup_id,
                ABSPATH . '.htaccess.redco-backup-*'
            );

            $htaccess_removed_count = 0;
            $htaccess_errors = array();

            // Check for specific backup file
            $backup_htaccess = ABSPATH . '.htaccess.backup.' . $backup_id;
            if (file_exists($backup_htaccess)) {
                if (unlink($backup_htaccess)) {
                    $htaccess_removed_count++;
                    error_log("REDCO CLEANUP: Successfully removed backup .htaccess: {$backup_htaccess}");
                } else {
                    $htaccess_errors[] = "Failed to remove backup .htaccess: {$backup_htaccess}";
                }
            }

            // Check for redco-backup pattern files (created by engine)
            $htaccess_files = glob(ABSPATH . '.htaccess.redco-backup-*');
            if ($htaccess_files) {
                foreach ($htaccess_files as $file) {
                    if (unlink($file)) {
                        $htaccess_removed_count++;
                        error_log("REDCO CLEANUP: Successfully removed engine backup .htaccess: {$file}");
                    } else {
                        $htaccess_errors[] = "Failed to remove engine backup .htaccess: {$file}";
                    }
                }
            }

            $cleanup_results['backup_htaccess_removed'] = ($htaccess_removed_count > 0 || empty($htaccess_files));
            if (!empty($htaccess_errors)) {
                $cleanup_results['errors'] = array_merge($cleanup_results['errors'], $htaccess_errors);
            }

            error_log("REDCO CLEANUP: Removed {$htaccess_removed_count} backup .htaccess files");

            // 3. Update stats efficiently (without heavy recalculation)
            $stats_updated = $this->update_stats_efficiently_after_rollback();
            $cleanup_results['stats_updated'] = $stats_updated;
            if ($stats_updated) {
                error_log("REDCO CLEANUP: Successfully updated stats after rollback");
            } else {
                $cleanup_results['errors'][] = "Failed to update stats after rollback";
            }

        } catch (Exception $e) {
            $cleanup_results['errors'][] = "Cleanup exception: " . $e->getMessage();
            error_log("REDCO CLEANUP ERROR: " . $e->getMessage());
        }

        $success_count = ($cleanup_results['backup_folder_removed'] ? 1 : 0) +
                        ($cleanup_results['backup_htaccess_removed'] ? 1 : 0) +
                        ($cleanup_results['stats_updated'] ? 1 : 0);

        error_log("REDCO CLEANUP: Completed {$success_count}/3 cleanup operations for backup {$backup_id}");

        return $cleanup_results;
    }

    /**
     * MEMORY-EFFICIENT: Safely remove directory without loading all files into memory
     */
    private function remove_directory_safely($dir) {
        if (!is_dir($dir)) {
            return true;
        }

        try {
            // Use iterator to avoid loading all files into memory at once
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isDir()) {
                    rmdir($file->getRealPath());
                } else {
                    unlink($file->getRealPath());
                }
            }

            return rmdir($dir);
        } catch (Exception $e) {
            error_log("REDCO CLEANUP ERROR: Failed to remove directory {$dir}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * MEMORY-EFFICIENT: Update stats without heavy recalculation
     */
    private function update_stats_efficiently_after_rollback() {
        try {
            // Get current stats with detailed logging
            $current_stats = get_option('redco_diagnostic_stats', array());
            $original_fixes = $current_stats['fixes_applied'] ?? 0;

            error_log("REDCO STATS: Before rollback - fixes_applied: {$original_fixes}");
            error_log("REDCO STATS: Current stats structure: " . json_encode(array_keys($current_stats)));

            // Simple decrements without heavy recalculation
            if (isset($current_stats['fixes_applied']) && $current_stats['fixes_applied'] > 0) {
                $current_stats['fixes_applied']--;
                error_log("REDCO STATS: Decremented fixes_applied from {$original_fixes} to " . $current_stats['fixes_applied']);
            } else {
                error_log("REDCO STATS: No fixes_applied to decrement (current: {$original_fixes})");
            }

            // Update timestamps
            $current_stats['last_updated'] = time();
            $current_stats['last_rollback'] = time();

            // Update the option with verification
            $update_result = update_option('redco_diagnostic_stats', $current_stats);

            if ($update_result) {
                // Verify the update worked
                $verification_stats = get_option('redco_diagnostic_stats', array());
                $new_fixes = $verification_stats['fixes_applied'] ?? 0;
                error_log("REDCO STATS: Update successful - verified fixes_applied: {$new_fixes}");

                // Clear any stats-related caches
                delete_transient('redco_diagnostic_stats');
                wp_cache_delete('redco_diagnostic_stats');

                return true;
            } else {
                error_log("REDCO STATS: Update failed - update_option returned false");
                return false;
            }

        } catch (Exception $e) {
            error_log("REDCO STATS UPDATE ERROR: " . $e->getMessage());
            return false;
        }
    }

    /**
     * CRITICAL FIX: Clear all caches after rollback to ensure changes are visible
     */
    private function clear_all_caches_for_rollback() {
        // Clear PHP file status cache
        clearstatcache();

        // Clear OPcache if available
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

        // Clear WordPress object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Clear any transients related to diagnostics
        delete_transient('redco_diagnostic_results');
        delete_transient('redco_diagnostic_cache');
        delete_transient('redco_performance_score');

        // Clear plugin-specific caches
        delete_option('redco_diagnostic_cache_timestamp');

        error_log("REDCO ROLLBACK: All caches cleared for rollback");
    }

    /**
     * CRITICAL FIX: Invalidate diagnostic cache to force fresh scan
     */
    private function invalidate_diagnostic_cache() {
        // Remove cached diagnostic results
        delete_transient('redco_diagnostic_results');
        delete_transient('redco_diagnostic_scan_cache');
        delete_option('redco_diagnostic_last_scan');

        // Force cache timestamp update
        update_option('redco_diagnostic_cache_invalidated', time());

        error_log("REDCO ROLLBACK: Diagnostic cache invalidated");
    }

    /**
     * CRITICAL FIX: Update fix history to remove rolled back session and clear fixed issues - ENHANCED
     */
    private function update_fix_history_for_rollback($backup_id) {
        $fix_history = get_option('redco_diagnostic_fix_history', array());
        $fixed_issues = get_option('redco_fixed_issues', array());

        error_log("REDCO ROLLBACK: Starting fix history update for backup {$backup_id}");
        error_log("REDCO ROLLBACK: Current fix history has " . count($fix_history) . " sessions");
        // MEMORY FIX: Don't log full history data to prevent memory issues
        // error_log("REDCO ROLLBACK: Full fix history data: " . json_encode($fix_history));

        // Find the session that corresponds to this backup and collect fixed issue IDs
        $rolled_back_issue_ids = array();
        $updated_history = array();
        $sessions_removed = 0;

        foreach ($fix_history as $index => $session) {
            $session_rollback_id = $session['rollback_id'] ?? '';
            $session_backup_id = $session['backup_id'] ?? '';
            $session_timestamp = $session['timestamp'] ?? '';

            error_log("REDCO ROLLBACK: Checking session {$index} - rollback_id: '{$session_rollback_id}', backup_id: '{$session_backup_id}', timestamp: {$session_timestamp}");
            // MEMORY FIX: Don't log full session data to prevent memory issues
            // error_log("REDCO ROLLBACK: Session data: " . json_encode($session));

            // ENHANCED: Multiple strategies to match the session
            $should_remove = false;
            $match_strategy = '';

            // Strategy 1: Direct rollback_id match
            if (!empty($session_rollback_id) && $session_rollback_id === $backup_id) {
                $should_remove = true;
                $match_strategy = 'rollback_id';
                error_log("REDCO ROLLBACK: Match found via rollback_id");
            }
            // Strategy 2: backup_id match (alternative field)
            elseif (!empty($session_backup_id) && $session_backup_id === $backup_id) {
                $should_remove = true;
                $match_strategy = 'backup_id';
                error_log("REDCO ROLLBACK: Match found via backup_id");
            }
            // Strategy 3: Check if backup_id contains session timestamp (for migrated sessions)
            elseif (!empty($session_timestamp) && strpos($backup_id, (string)$session_timestamp) !== false) {
                $should_remove = true;
                $match_strategy = 'timestamp';
                error_log("REDCO ROLLBACK: Match found via timestamp in backup_id");
            }
            // Strategy 4: Check details for rollback_id matches
            elseif (isset($session['details']) && is_array($session['details'])) {
                foreach ($session['details'] as $detail) {
                    if (isset($detail['rollback_id']) && $detail['rollback_id'] === $backup_id) {
                        $should_remove = true;
                        $match_strategy = 'details_rollback_id';
                        error_log("REDCO ROLLBACK: Match found via details rollback_id");
                        break;
                    }
                }
            }

            error_log("REDCO ROLLBACK: Session {$index} - should_remove: " . ($should_remove ? 'YES' : 'NO') . ", match_strategy: {$match_strategy}");

            if ($should_remove) {
                // Collect issue IDs from this session's fix details
                if (isset($session['details']) && is_array($session['details'])) {
                    foreach ($session['details'] as $fix_detail) {
                        if (isset($fix_detail['issue_id']) && $fix_detail['success']) {
                            $rolled_back_issue_ids[] = $fix_detail['issue_id'];
                        }
                    }
                }

                // Skip this session - it's being rolled back
                $sessions_removed++;
                error_log("REDCO ROLLBACK: Removing session {$index} from history for backup {$backup_id}");
                continue;
            }
            $updated_history[] = $session;
        }

        // CRITICAL FIX: Remove rolled back issues from fixed_issues option
        foreach ($rolled_back_issue_ids as $issue_id) {
            if (isset($fixed_issues[$issue_id])) {
                unset($fixed_issues[$issue_id]);
                error_log("REDCO ROLLBACK: Removed issue {$issue_id} from fixed_issues list");
            }
        }

        // CRITICAL DEBUG: Log the updated history before saving (MEMORY EFFICIENT)
        error_log("REDCO ROLLBACK: About to update database - sessions_removed: {$sessions_removed}");
        error_log("REDCO ROLLBACK: Original history count: " . count($fix_history));
        error_log("REDCO ROLLBACK: Updated history count: " . count($updated_history));
        // MEMORY FIX: Don't log full updated history to prevent memory issues
        // error_log("REDCO ROLLBACK: Updated history data: " . json_encode($updated_history));

        // Update both options
        $history_updated = update_option('redco_diagnostic_fix_history', $updated_history);
        $issues_updated = update_option('redco_fixed_issues', $fixed_issues);

        // CRITICAL DEBUG: Verify the update actually worked
        $verification_history = get_option('redco_diagnostic_fix_history', array());
        error_log("REDCO ROLLBACK: Database updates - sessions_removed: {$sessions_removed}, history_updated: " . ($history_updated ? 'true' : 'false') . ", issues_updated: " . ($issues_updated ? 'true' : 'false'));
        error_log("REDCO ROLLBACK: Updated history now has " . count($updated_history) . " sessions (was " . count($fix_history) . ")");
        error_log("REDCO ROLLBACK: Verification - database now has " . count($verification_history) . " sessions");

        if (count($verification_history) !== count($updated_history)) {
            error_log("REDCO ROLLBACK: ❌ CRITICAL ERROR - Database update failed! Expected " . count($updated_history) . " sessions, but database has " . count($verification_history));
        } else {
            error_log("REDCO ROLLBACK: ✅ Database update verified successfully");
        }

        // Also record the rollback action
        $rollback_session = array(
            'timestamp' => time(),
            'action' => 'rollback',
            'backup_id' => $backup_id,
            'message' => 'Fixes rolled back successfully',
            'rolled_back_issues' => $rolled_back_issue_ids,
            'sessions_removed' => $sessions_removed
        );

        $rollback_history = get_option('redco_rollback_history', array());
        $rollback_history[] = $rollback_session;

        // Keep only last 20 rollback records
        if (count($rollback_history) > 20) {
            $rollback_history = array_slice($rollback_history, -20);
        }

        update_option('redco_rollback_history', $rollback_history);

        error_log("REDCO ROLLBACK: Fix history updated for rollback of backup {$backup_id}, removed {$sessions_removed} sessions, cleared " . count($rolled_back_issue_ids) . " fixed issues");

        // TARGETED APPROACH: No cache clearing needed at all!
        // The database update is sufficient - the frontend will get fresh data on next AJAX request
        // This is just removing one item from a list, not a system-wide change

        error_log("REDCO ROLLBACK: Database updated successfully - no cache clearing needed for single item removal");
    }

    /**
     * CRITICAL FIX: AJAX handler to clear fixed issues state after rollback
     */
    public function ajax_clear_fixed_issues_state() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $backup_id = sanitize_text_field($_POST['backup_id'] ?? '');
        if (empty($backup_id)) {
            wp_send_json_error('Backup ID is required');
            return;
        }

        // Clear fixed issues related to this backup
        $this->clear_fixed_issues_for_backup($backup_id);

        wp_send_json_success(array(
            'message' => 'Fixed issues state cleared successfully',
            'backup_id' => $backup_id
        ));
    }

    /**
     * CRITICAL FIX: Clear fixed issues for a specific backup
     */
    private function clear_fixed_issues_for_backup($backup_id) {
        $fixed_issues = get_option('redco_fixed_issues', array());
        $cleared_count = 0;

        // Find and remove fixed issues that match this backup
        foreach ($fixed_issues as $issue_id => $fix_info) {
            if (isset($fix_info['rollback_id']) && $fix_info['rollback_id'] === $backup_id) {
                unset($fixed_issues[$issue_id]);
                $cleared_count++;
                error_log("REDCO: Cleared fixed issue {$issue_id} for backup {$backup_id}");
            }
        }

        // Update the option
        update_option('redco_fixed_issues', $fixed_issues);

        error_log("REDCO: Cleared {$cleared_count} fixed issues for backup {$backup_id}");
        return $cleared_count;
    }

    /**
     * CRITICAL FIX: Recalculate diagnostic statistics after rollback
     */
    private function recalculate_diagnostic_statistics_after_rollback() {
        error_log("REDCO ROLLBACK: Starting statistics recalculation after rollback");

        // Get current diagnostic results to recalculate stats
        $diagnostic_results = get_option('redco_diagnostic_results', array());
        $fix_history = get_option('redco_diagnostic_fix_history', array());
        $fixed_issues = get_option('redco_fixed_issues', array());

        // Calculate current statistics
        $total_issues = isset($diagnostic_results['issues']) ? count($diagnostic_results['issues']) : 0;
        $critical_issues = 0;
        $auto_fixable_issues = 0;

        // Count issues by severity and auto-fixable status
        if (isset($diagnostic_results['issues']) && is_array($diagnostic_results['issues'])) {
            foreach ($diagnostic_results['issues'] as $issue) {
                if (isset($issue['severity']) && $issue['severity'] === 'critical') {
                    $critical_issues++;
                }
                if (isset($issue['auto_fixable']) && $issue['auto_fixable']) {
                    $auto_fixable_issues++;
                }
            }
        }

        // Calculate fixes applied (from fix history)
        $fixes_applied = count($fix_history);

        // Calculate health and performance scores
        $health_score = $this->calculate_health_score_from_issues($diagnostic_results['issues'] ?? array());
        $performance_score = isset($diagnostic_results['performance_score']) ? $diagnostic_results['performance_score'] : 68;

        // Update diagnostic stats option
        $updated_stats = array(
            'health_score' => $health_score,
            'performance_score' => $performance_score,
            'issues_found' => $total_issues,
            'critical_issues' => $critical_issues,
            'auto_fixable_issues' => $auto_fixable_issues,
            'fixes_applied' => $fixes_applied,
            'last_scan_time' => isset($diagnostic_results['timestamp']) ? $diagnostic_results['timestamp'] : time(),
            'scan_frequency' => 'weekly',
            'auto_fix_enabled' => true,
            'emergency_mode_active' => false,
            'last_updated' => time()
        );

        // Update the stats in database
        update_option('redco_diagnostic_stats', $updated_stats);

        // Clear stats-related caches
        delete_transient('redco_diagnostic_stats');
        wp_cache_delete('redco_diagnostic_stats');

        error_log("REDCO ROLLBACK: Statistics updated - Issues: {$total_issues}, Critical: {$critical_issues}, Auto-fixable: {$auto_fixable_issues}, Fixes: {$fixes_applied}");

        return $updated_stats;
    }

    /**
     * CRITICAL FIX: Calculate health score from issues array
     */
    private function calculate_health_score_from_issues($issues) {
        if (empty($issues) || !is_array($issues)) {
            return 100; // Perfect score if no issues
        }

        $total_issues = count($issues);
        $critical_count = 0;
        $high_count = 0;
        $medium_count = 0;

        foreach ($issues as $issue) {
            $severity = isset($issue['severity']) ? strtolower($issue['severity']) : 'medium';
            switch ($severity) {
                case 'critical':
                    $critical_count++;
                    break;
                case 'high':
                    $high_count++;
                    break;
                case 'medium':
                default:
                    $medium_count++;
                    break;
            }
        }

        // Calculate weighted score (critical issues have more impact)
        $score = 100;
        $score -= ($critical_count * 25); // Critical issues: -25 points each
        $score -= ($high_count * 15);     // High issues: -15 points each
        $score -= ($medium_count * 5);    // Medium issues: -5 points each

        // Ensure score doesn't go below 0
        return max(0, min(100, $score));
    }

    /**
     * CRITICAL FIX: Check for restored issues after rollback
     */
    private function check_for_restored_issues_after_rollback($backup_id) {
        error_log("REDCO ROLLBACK: Checking for restored issues after rollback of backup {$backup_id}");

        // Get the fix history to see what issues were rolled back
        $fix_history = get_option('redco_diagnostic_fix_history', array());
        $rollback_history = get_option('redco_rollback_history', array());

        // Find the most recent rollback entry for this backup
        $rolled_back_issues = array();
        foreach (array_reverse($rollback_history) as $rollback_entry) {
            if (isset($rollback_entry['backup_id']) && $rollback_entry['backup_id'] === $backup_id) {
                if (isset($rollback_entry['rolled_back_issues']) && is_array($rollback_entry['rolled_back_issues'])) {
                    $rolled_back_issues = $rollback_entry['rolled_back_issues'];
                    break;
                }
            }
        }

        if (empty($rolled_back_issues)) {
            error_log("REDCO ROLLBACK: No rolled back issues found for backup {$backup_id}");
            return array();
        }

        error_log("REDCO ROLLBACK: Found " . count($rolled_back_issues) . " rolled back issue IDs: " . implode(', ', $rolled_back_issues));

        // Run a quick diagnostic check to see if these issues still exist
        $restored_issues = array();

        // For now, we'll create basic issue data for the rolled-back issues
        // In a full implementation, you would re-run specific diagnostic checks
        foreach ($rolled_back_issues as $issue_id) {
            $restored_issues[] = array(
                'id' => $issue_id,
                'title' => 'Rolled Back Issue: ' . $issue_id,
                'description' => 'This issue was previously fixed but has been restored due to rollback.',
                'severity' => 'medium',
                'category' => 'rollback',
                'auto_fixable' => true,
                'type' => 'restored_issue',
                'restored_from_backup' => $backup_id,
                'restored_at' => time()
            );
        }

        error_log("REDCO ROLLBACK: Generated " . count($restored_issues) . " restored issue entries");
        return $restored_issues;
    }

    /**
     * CRITICAL FIX: Generate restored issue data for UI updates
     */
    private function generate_restored_issue_data($backup_id) {
        // This would generate data about what issues were restored
        // For now, return basic structure
        return array(
            'backup_id' => $backup_id,
            'restored_at' => time(),
            'message' => 'Previous fixes have been rolled back'
        );
    }

    /**
     * CRITICAL FIX: Record rollback in history for tracking
     */
    private function record_rollback_in_history($backup_id, $rollback_result) {
        // Update fix history to remove the rolled back session
        $this->update_fix_history_for_rollback($backup_id);

        // Record the rollback action in rollback history
        $rollback_session = array(
            'timestamp' => time(),
            'action' => 'rollback',
            'backup_id' => $backup_id,
            'message' => $rollback_result['message'] ?? 'Fixes rolled back successfully',
            'files_restored' => $rollback_result['files_restored'] ?? 0,
            'options_restored' => $rollback_result['options_restored'] ?? 0,
            'operation_details' => array(
                'fixed_issues_cleared' => $rollback_result['fixed_issues_cleared'] ?? false,
                'scan_caches_cleared' => $rollback_result['scan_caches_cleared'] ?? false,
                'diagnostic_state_reset' => $rollback_result['diagnostic_state_reset'] ?? false
            )
        );

        $rollback_history = get_option('redco_rollback_history', array());
        $rollback_history[] = $rollback_session;

        // Keep only last 20 rollback records
        if (count($rollback_history) > 20) {
            $rollback_history = array_slice($rollback_history, -20);
        }

        update_option('redco_rollback_history', $rollback_history);

        error_log("REDCO ROLLBACK: Rollback recorded in history for backup {$backup_id}");
    }

    /**
     * PROFESSIONAL SOLUTION: Standardized error response system
     */
    private function send_standardized_error($error_code, $message, $context = array()) {
        $error_response = array(
            'message' => $message,
            'error_code' => $error_code,
            'timestamp' => current_time('mysql'),
            'context' => defined('WP_DEBUG') && WP_DEBUG ? $context : null
        );

        error_log("REDCO ERROR [{$error_code}]: {$message}");
        wp_send_json_error($error_response);
    }

    /**
     * PROFESSIONAL SOLUTION: Extract and validate target data from request
     */
    private function extract_target_data($request_data) {
        $issue_id = sanitize_text_field($request_data['issue_id'] ?? '');
        $opportunity_id = sanitize_text_field($request_data['opportunity_id'] ?? '');

        if (!empty($issue_id)) {
            return array(
                'success' => true,
                'id' => $issue_id,
                'type' => 'issue',
                'source_field' => 'issue_id'
            );
        }

        if (!empty($opportunity_id)) {
            return array(
                'success' => true,
                'id' => $opportunity_id,
                'type' => 'optimization_opportunity',
                'source_field' => 'opportunity_id'
            );
        }

        return array(
            'success' => false,
            'message' => 'No valid target ID provided (issue_id or opportunity_id required)'
        );
    }

    /**
     * PROFESSIONAL SOLUTION: Unified target resolution system
     */
    private function resolve_target_from_scan($target_id, $scan_results) {
        if (!isset($scan_results['issues']) || !is_array($scan_results['issues'])) {
            return array(
                'success' => false,
                'message' => 'No scan results available'
            );
        }

        foreach ($scan_results['issues'] as $issue) {
            if (!is_array($issue) || !isset($issue['id'])) {
                continue;
            }

            if ($issue['id'] === $target_id) {
                return array(
                    'success' => true,
                    'target' => $issue,
                    'index' => array_search($issue, $scan_results['issues'])
                );
            }
        }

        return array(
            'success' => false,
            'message' => "Target with ID '{$target_id}' not found in current scan results"
        );
    }







    /**
     * DEVELOPMENT HELPER: Clear all diagnostic data for fresh testing
     */
    public function ajax_clear_all_diagnostic_data() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $cleared_items = array();
            $initial_memory = memory_get_usage();
            error_log('REDCO: Starting data clear with memory usage: ' . round($initial_memory / 1024 / 1024, 2) . 'MB');

            // Clear diagnostic results
            if (delete_option('redco_diagnostic_results')) {
                $cleared_items[] = 'Diagnostic results';
            }

            // Clear diagnostic stats
            if (delete_option('redco_diagnostic_stats')) {
                $cleared_items[] = 'Diagnostic stats';
            }

            // Clear fix history
            if (delete_option('redco_diagnostic_fix_history')) {
                $cleared_items[] = 'Fix history';
            }

            // Clear fixed issues
            if (delete_option('redco_fixed_issues')) {
                $cleared_items[] = 'Fixed issues';
            }

            // Clear scan cache
            if (delete_option('redco_scan_cache')) {
                $cleared_items[] = 'Scan cache';
            }

            // Clear last scan time
            if (delete_option('redco_last_scan_time')) {
                $cleared_items[] = 'Last scan time';
            }

            // Clear optimization logs
            if (delete_option('redco_optimization_logs')) {
                $cleared_items[] = 'Optimization logs';
            }

            // Clear scan progress
            if (delete_option('redco_scan_progress')) {
                $cleared_items[] = 'Scan progress';
            }

            // Clear all diagnostic-related transients
            $transients_cleared = 0;
            $diagnostic_transients = array(
                'redco_compression_check',
                'redco_cache_headers_check',
                'redco_autoload_size',
                'redco_database_size',
                'redco_ttfb_measurement',
                'redco_render_blocking_resources',
                'redco_security_headers_check',
                'redco_optimization_opportunities',
                'redco_diagnostic_results',
                'redco_diagnostic_stats',
                'redco_recent_fixes'
            );

            foreach ($diagnostic_transients as $transient) {
                if (delete_transient($transient)) {
                    $transients_cleared++;
                }
            }

            if ($transients_cleared > 0) {
                $cleared_items[] = "{$transients_cleared} transients";
            }

            // MEMORY-EFFICIENT: Clear only specific cache keys instead of full flush
            wp_cache_delete('redco_diagnostic_results');
            wp_cache_delete('redco_diagnostic_stats');
            wp_cache_delete('redco_recent_fixes');
            $cleared_items[] = 'Specific cache keys cleared';

            // MEMORY-EFFICIENT: Clear database tables with error handling
            global $wpdb;
            $tables_cleared = 0;

            try {
                // Clear fix history table
                $fix_history_table = $wpdb->prefix . 'redco_fix_history';
                if ($wpdb->get_var("SHOW TABLES LIKE '{$fix_history_table}'") == $fix_history_table) {
                    $deleted_rows = $wpdb->query("DELETE FROM {$fix_history_table}");
                    if ($deleted_rows !== false) {
                        $tables_cleared++;
                        $cleared_items[] = "Fix history table ({$deleted_rows} rows)";
                    }
                }

                // Clear scheduled fixes table
                $scheduled_fixes_table = $wpdb->prefix . 'redco_scheduled_fixes';
                if ($wpdb->get_var("SHOW TABLES LIKE '{$scheduled_fixes_table}'") == $scheduled_fixes_table) {
                    $deleted_rows = $wpdb->query("DELETE FROM {$scheduled_fixes_table}");
                    if ($deleted_rows !== false) {
                        $tables_cleared++;
                        $cleared_items[] = "Scheduled fixes table ({$deleted_rows} rows)";
                    }
                }
            } catch (Exception $e) {
                error_log('REDCO: Database clearing error (non-fatal): ' . $e->getMessage());
                $cleared_items[] = 'Database tables (some errors occurred)';
            }

            // Clear browser localStorage cache
            $cleared_items[] = 'Browser localStorage cache (will be cleared on page load)';

            $final_memory = memory_get_usage();
            $memory_used = round(($final_memory - $initial_memory) / 1024 / 1024, 2);
            error_log('REDCO: Diagnostic data cleared with memory-efficient approach. Memory used: ' . $memory_used . 'MB');

            wp_send_json_success(array(
                'message' => 'All diagnostic data cleared successfully!',
                'cleared_items' => $cleared_items,
                'total_items' => count($cleared_items),
                'memory_used' => $memory_used . 'MB'
            ));

        } catch (Exception $e) {
            error_log('REDCO ERROR: Failed to clear diagnostic data: ' . $e->getMessage());
            wp_send_json_error('Failed to clear diagnostic data: ' . $e->getMessage());
        }
    }

    /**
     * MEMORY-EFFICIENT: Simple diagnostic data clearing (minimal memory usage)
     */
    public function ajax_simple_clear_diagnostic_data() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            // Clear only essential options without memory-intensive operations
            $essential_options = array(
                'redco_diagnostic_results',
                'redco_diagnostic_stats',
                'redco_diagnostic_fix_history',
                'redco_fixed_issues'
            );

            $cleared_count = 0;
            foreach ($essential_options as $option) {
                if (delete_option($option)) {
                    $cleared_count++;
                }
            }

            // Clear essential transients only
            $essential_transients = array(
                'redco_diagnostic_results',
                'redco_recent_fixes'
            );

            foreach ($essential_transients as $transient) {
                delete_transient($transient);
            }

            error_log('REDCO: Simple data clear completed - ' . $cleared_count . ' options cleared');

            wp_send_json_success(array(
                'message' => 'Essential diagnostic data cleared successfully!',
                'cleared_count' => $cleared_count,
                'method' => 'simple'
            ));

        } catch (Exception $e) {
            error_log('REDCO ERROR: Simple clear failed: ' . $e->getMessage());
            wp_send_json_error('Simple clear failed: ' . $e->getMessage());
        }
    }

    /**
     * EMERGENCY CONTROL: Reset all fixes and diagnostic data
     */
    public function ajax_reset_all_fixes() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $operation_id = 'reset_all_' . time() . '_' . wp_generate_password(8, false);
            error_log("REDCO RESET: Starting complete diagnostic reset operation {$operation_id}");

            $cleared_items = array();
            $initial_memory = memory_get_usage();

            // 1. Clear fix history
            if (delete_option('redco_diagnostic_fix_history')) {
                $cleared_items[] = 'Fix history';
                error_log("REDCO RESET: Fix history cleared");
            }

            // 2. Clear fixed issues
            if (delete_option('redco_fixed_issues')) {
                $cleared_items[] = 'Fixed issues';
                error_log("REDCO RESET: Fixed issues cleared");
            }

            // 3. Clear diagnostic results
            if (delete_option('redco_diagnostic_results')) {
                $cleared_items[] = 'Diagnostic results';
                error_log("REDCO RESET: Diagnostic results cleared");
            }

            // 4. Reset diagnostic statistics
            $default_stats = array(
                'health_score' => 100,
                'performance_score' => 68,
                'issues_found' => 0,
                'critical_issues' => 0,
                'auto_fixable_issues' => 0,
                'fixes_applied' => 0,
                'last_scan_time' => 0,
                'scan_frequency' => 'weekly',
                'auto_fix_enabled' => true,
                'emergency_mode_active' => false,
                'last_reset' => time()
            );
            update_option('redco_diagnostic_stats', $default_stats);
            $cleared_items[] = 'Diagnostic statistics (reset to defaults)';
            error_log("REDCO RESET: Diagnostic statistics reset");

            // 5. Clear rollback history
            if (delete_option('redco_rollback_history')) {
                $cleared_items[] = 'Rollback history';
                error_log("REDCO RESET: Rollback history cleared");
            }

            // 6. Clear scan cache and related options
            $cache_options = array(
                'redco_scan_cache',
                'redco_last_scan_time',
                'redco_optimization_logs',
                'redco_scan_progress'
            );

            foreach ($cache_options as $option) {
                if (delete_option($option)) {
                    $cleared_items[] = str_replace('redco_', '', $option);
                }
            }

            // 7. Clear all diagnostic-related transients
            $transients_cleared = 0;
            $diagnostic_transients = array(
                'redco_compression_check',
                'redco_cache_headers_check',
                'redco_autoload_size',
                'redco_database_size',
                'redco_ttfb_measurement',
                'redco_render_blocking_resources',
                'redco_security_headers_check',
                'redco_optimization_opportunities',
                'redco_diagnostic_results',
                'redco_diagnostic_stats',
                'redco_recent_fixes'
            );

            foreach ($diagnostic_transients as $transient) {
                if (delete_transient($transient)) {
                    $transients_cleared++;
                }
            }

            if ($transients_cleared > 0) {
                $cleared_items[] = "{$transients_cleared} transients";
            }

            // 8. Clear WordPress object cache for diagnostic keys
            wp_cache_delete('redco_diagnostic_results');
            wp_cache_delete('redco_diagnostic_stats');
            wp_cache_delete('redco_recent_fixes');
            wp_cache_delete('redco_fixed_issues');
            $cleared_items[] = 'WordPress object cache keys';

            // 9. Clear database tables if they exist
            global $wpdb;
            $tables_cleared = 0;

            try {
                // Clear fix history table
                $fix_history_table = $wpdb->prefix . 'redco_fix_history';
                if ($wpdb->get_var("SHOW TABLES LIKE '{$fix_history_table}'") == $fix_history_table) {
                    $deleted_rows = $wpdb->query("DELETE FROM {$fix_history_table}");
                    if ($deleted_rows !== false) {
                        $tables_cleared++;
                        $cleared_items[] = "Fix history table ({$deleted_rows} rows)";
                    }
                }

                // Clear scheduled fixes table
                $scheduled_fixes_table = $wpdb->prefix . 'redco_scheduled_fixes';
                if ($wpdb->get_var("SHOW TABLES LIKE '{$scheduled_fixes_table}'") == $scheduled_fixes_table) {
                    $deleted_rows = $wpdb->query("DELETE FROM {$scheduled_fixes_table}");
                    if ($deleted_rows !== false) {
                        $tables_cleared++;
                        $cleared_items[] = "Scheduled fixes table ({$deleted_rows} rows)";
                    }
                }
            } catch (Exception $e) {
                error_log('REDCO RESET: Database clearing error (non-fatal): ' . $e->getMessage());
                $cleared_items[] = 'Database tables (some errors occurred)';
            }

            $final_memory = memory_get_usage();
            $memory_used = round(($final_memory - $initial_memory) / 1024 / 1024, 2);

            error_log("REDCO RESET: Complete reset operation {$operation_id} finished successfully");

            wp_send_json_success(array(
                'message' => 'All diagnostic data has been reset successfully!',
                'operation_id' => $operation_id,
                'cleared_items' => $cleared_items,
                'total_items' => count($cleared_items),
                'memory_used' => $memory_used . 'MB',
                'reset_timestamp' => time(),
                'ui_updates_required' => array(
                    'refresh_all_sections' => true,
                    'reset_statistics' => true,
                    'clear_recent_fixes' => true,
                    'clear_recent_issues' => true
                )
            ));

        } catch (Exception $e) {
            error_log('REDCO RESET ERROR: Failed to reset diagnostic data: ' . $e->getMessage());
            wp_send_json_error('Failed to reset diagnostic data: ' . $e->getMessage());
        }
    }

    /**
     * EMERGENCY HELPER: Rollback .htaccess security headers to fix Internal Server Error
     */
    public function ajax_rollback_htaccess_security_headers() {
        // Security check
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        try {
            $htaccess_file = ABSPATH . '.htaccess';

            if (!file_exists($htaccess_file)) {
                wp_send_json_error('.htaccess file not found');
                return;
            }

            if (!is_writable($htaccess_file)) {
                wp_send_json_error('.htaccess file is not writable');
                return;
            }

            // Read current content
            $current_content = file_get_contents($htaccess_file);
            if ($current_content === false) {
                wp_send_json_error('Could not read .htaccess file');
                return;
            }

            $original_content = $current_content;
            $changes_made = array();

            // Remove security headers added by Redco Optimizer (multiple patterns)
            $patterns_to_remove = array(
                // Pattern 1: Headers with comment
                '/\n?# Security Headers - Added by Redco Optimizer\s*\n<IfModule mod_headers\.c>\s*\n(?:.*?Header always set.*?\n)*.*?<\/IfModule>\s*\n?/s',

                // Pattern 2: BEGIN/END block
                '/\n?# BEGIN Redco Optimizer Security Headers.*?# END Redco Optimizer Security Headers\s*\n?/s',

                // Pattern 3: Simple IfModule block with headers
                '/\n?<IfModule mod_headers\.c>\s*\n(?:.*?Header always set X-Content-Type-Options.*?\n)?(?:.*?Header always set X-Frame-Options.*?\n)?(?:.*?Header always set X-XSS-Protection.*?\n)?(?:.*?Header always set Strict-Transport-Security.*?\n)?.*?<\/IfModule>\s*\n?/s'
            );

            foreach ($patterns_to_remove as $index => $pattern) {
                $new_content = preg_replace($pattern, '', $current_content);
                if ($new_content !== $current_content) {
                    $current_content = $new_content;
                    $changes_made[] = "Removed security headers pattern " . ($index + 1);
                }
            }

            // Also remove any standalone security header lines
            $header_lines = array(
                '/\s*Header always set X-Content-Type-Options.*?\n/',
                '/\s*Header always set X-Frame-Options.*?\n/',
                '/\s*Header always set X-XSS-Protection.*?\n/',
                '/\s*Header always set Strict-Transport-Security.*?\n/',
                '/\s*Header always set Referrer-Policy.*?\n/',
                '/\s*Header always set Permissions-Policy.*?\n/'
            );

            foreach ($header_lines as $pattern) {
                $new_content = preg_replace($pattern, '', $current_content);
                if ($new_content !== $current_content) {
                    $current_content = $new_content;
                    $changes_made[] = "Removed standalone security header line";
                }
            }

            // Clean up multiple empty lines
            $current_content = preg_replace('/\n{3,}/', "\n\n", $current_content);

            // Write the cleaned content back
            if ($current_content !== $original_content) {
                if (file_put_contents($htaccess_file, $current_content)) {
                    error_log('REDCO: Successfully rolled back .htaccess security headers');

                    wp_send_json_success(array(
                        'message' => 'Security headers successfully removed from .htaccess',
                        'changes_made' => $changes_made,
                        'bytes_removed' => strlen($original_content) - strlen($current_content)
                    ));
                } else {
                    wp_send_json_error('Failed to write cleaned content to .htaccess');
                }
            } else {
                wp_send_json_success(array(
                    'message' => 'No security headers found to remove',
                    'changes_made' => array('No changes needed')
                ));
            }

        } catch (Exception $e) {
            error_log('REDCO ERROR: Failed to rollback .htaccess: ' . $e->getMessage());
            wp_send_json_error('Failed to rollback .htaccess: ' . $e->getMessage());
        }
    }



    /**
     * AJAX handler for emergency mode
     */
    public function ajax_emergency_mode() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $action = sanitize_text_field($_POST['emergency_action'] ?? 'activate');

        if ($action === 'activate') {
            $this->activate_emergency_mode();
            wp_send_json_success(array(
                'message' => __('Emergency mode activated!', 'redco-optimizer'),
                'status' => 'active'
            ));
        } else {
            $this->deactivate_emergency_mode();
            wp_send_json_success(array(
                'message' => __('Emergency mode deactivated!', 'redco-optimizer'),
                'status' => 'inactive'
            ));
        }
    }

    /**
     * AJAX handler for getting "How to solve" tips
     */
    public function ajax_get_how_to_solve_tip() {
        // CRITICAL FIX: Use correct nonce that matches frontend
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $issue_data = sanitize_text_field($_POST['issue_data'] ?? '');
        if (empty($issue_data)) {
            wp_send_json_error('Issue data is required');
            return;
        }

        // Decode the issue data
        $issue = json_decode(stripslashes($issue_data), true);
        if (!$issue) {
            error_log('REDCO DEBUG: Invalid issue data received: ' . $issue_data);
            wp_send_json_error('Invalid issue data format');
            return;
        }

        // Debug logging
        error_log('REDCO DEBUG: How to solve tip requested for issue: ' . json_encode($issue));

        // Generate the dynamic help content
        $content = $this->get_how_to_solve_tip($issue);

        wp_send_json_success(array(
            'content' => $content,
            'issue_id' => $issue['id'] ?? 'unknown'
        ));
    }

    /**
     * Get "How to solve" tips for non-auto-fixable issues - DYNAMIC GENERATION
     */
    public function get_how_to_solve_tip($issue) {
        $issue_id = $issue['id'] ?? '';
        $issue_title = $issue['title'] ?? '';
        $issue_description = $issue['description'] ?? '';
        $category = $issue['category'] ?? 'general';
        $severity = $issue['severity'] ?? 'medium';

        // Generate dynamic guidance based on issue analysis
        return $this->generate_dynamic_guidance($issue_id, $issue_title, $issue_description, $category, $severity);
    }

    /**
     * Generate dynamic guidance based on issue analysis
     */
    private function generate_dynamic_guidance($issue_id, $title, $description, $category, $severity) {
        $guidance = array(
            'title' => '',
            'steps' => array(),
            'links' => array(),
            'warning' => ''
        );

        // Analyze issue content to generate appropriate guidance
        $keywords = $this->extract_keywords($title . ' ' . $description);

        // SSL/HTTPS Issues
        if ($this->contains_keywords($keywords, ['ssl', 'https', 'certificate', 'secure'])) {
            $guidance = $this->generate_ssl_guidance($title, $description);
        }
        // Database Issues
        elseif ($this->contains_keywords($keywords, ['database', 'mysql', 'db', 'autoload', 'query'])) {
            $guidance = $this->generate_database_guidance($title, $description);
        }
        // Performance Issues
        elseif ($this->contains_keywords($keywords, ['slow', 'loading', 'speed', 'performance', 'cache'])) {
            $guidance = $this->generate_performance_guidance($title, $description);
        }
        // Image Issues
        elseif ($this->contains_keywords($keywords, ['image', 'images', 'photo', 'picture', 'webp', 'compress'])) {
            $guidance = $this->generate_image_guidance($title, $description);
        }
        // CSS/JS Issues
        elseif ($this->contains_keywords($keywords, ['css', 'javascript', 'js', 'minify', 'render', 'blocking'])) {
            $guidance = $this->generate_assets_guidance($title, $description);
        }
        // WordPress Core Issues
        elseif ($this->contains_keywords($keywords, ['wordpress', 'wp', 'core', 'version', 'update'])) {
            $guidance = $this->generate_wordpress_guidance($title, $description);
        }
        // Plugin/Theme Issues
        elseif ($this->contains_keywords($keywords, ['plugin', 'theme', 'extension', 'addon'])) {
            $guidance = $this->generate_plugin_theme_guidance($title, $description);
        }
        // Security Issues
        elseif ($this->contains_keywords($keywords, ['security', 'password', 'login', 'permission', 'vulnerable'])) {
            $guidance = $this->generate_security_guidance($title, $description);
        }
        // Server Issues
        elseif ($this->contains_keywords($keywords, ['server', 'php', 'apache', 'nginx', 'hosting'])) {
            $guidance = $this->generate_server_guidance($title, $description);
        }
        // SEO Issues
        elseif ($this->contains_keywords($keywords, ['seo', 'meta', 'description', 'title', 'alt', 'sitemap'])) {
            $guidance = $this->generate_seo_guidance($title, $description);
        }
        // Fallback to category-based guidance
        else {
            $guidance = $this->generate_category_guidance($category, $title, $description);
        }

        return $this->format_guidance_html($guidance);
    }

    /**
     * Extract keywords from text for analysis
     */
    private function extract_keywords($text) {
        $text = strtolower($text);
        $text = preg_replace('/[^a-z0-9\s]/', ' ', $text);
        $words = array_filter(explode(' ', $text));

        // Remove common stop words
        $stop_words = array('the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'cannot', 'your', 'site', 'website', 'page', 'pages');

        return array_diff($words, $stop_words);
    }

    /**
     * Check if keywords contain specific terms
     */
    private function contains_keywords($keywords, $search_terms) {
        foreach ($search_terms as $term) {
            if (in_array($term, $keywords)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Generate SSL/HTTPS specific guidance
     */
    private function generate_ssl_guidance($title, $description) {
        return array(
            'title' => __('Enable SSL/HTTPS', 'redco-optimizer'),
            'steps' => array(
                __('1. Contact your hosting provider to install an SSL certificate', 'redco-optimizer'),
                __('2. Or use a free SSL certificate from Let\'s Encrypt', 'redco-optimizer'),
                __('3. Update WordPress Site URL to use https:// in Settings → General', 'redco-optimizer'),
                __('4. Install an SSL plugin like Really Simple SSL for automatic redirects', 'redco-optimizer'),
                __('5. Update any hardcoded HTTP links in your content', 'redco-optimizer'),
                __('6. Test your site to ensure all resources load over HTTPS', 'redco-optimizer')
            ),
            'links' => array(
                array('text' => __('WordPress Settings', 'redco-optimizer'), 'url' => admin_url('options-general.php')),
                array('text' => __('SSL Test Tool', 'redco-optimizer'), 'url' => 'https://www.ssllabs.com/ssltest/')
            ),
            'warning' => __('Always backup your site before making SSL changes. Test thoroughly after implementation.', 'redco-optimizer')
        );
    }

    /**
     * Generate database-specific guidance
     */
    private function generate_database_guidance($title, $description) {
        $guidance = array(
            'title' => __('Optimize Database', 'redco-optimizer'),
            'steps' => array(),
            'links' => array(),
            'warning' => __('Always backup your database before optimization!', 'redco-optimizer')
        );

        if (stripos($description, 'large') !== false || stripos($description, 'size') !== false) {
            $guidance['steps'] = array(
                __('1. Remove unnecessary post revisions and spam comments', 'redco-optimizer'),
                __('2. Clean up unused plugins and themes data', 'redco-optimizer'),
                __('3. Use a database optimization plugin like WP-Optimize', 'redco-optimizer'),
                __('4. Consider database table optimization via phpMyAdmin', 'redco-optimizer')
            );
        } elseif (stripos($description, 'autoload') !== false) {
            $guidance['steps'] = array(
                __('1. Identify plugins storing large amounts of autoload data', 'redco-optimizer'),
                __('2. Review and clean up options table autoload entries', 'redco-optimizer'),
                __('3. Contact plugin developers about excessive autoload usage', 'redco-optimizer'),
                __('4. Consider switching to plugins with better data management', 'redco-optimizer')
            );
        } else {
            $guidance['steps'] = array(
                __('1. Run database optimization to remove overhead', 'redco-optimizer'),
                __('2. Clean up post revisions, spam, and trash', 'redco-optimizer'),
                __('3. Remove unused plugin/theme data', 'redco-optimizer'),
                __('4. Schedule regular database maintenance', 'redco-optimizer')
            );
        }

        return $guidance;
    }

    /**
     * Generate performance-specific guidance
     */
    private function generate_performance_guidance($title, $description) {
        return array(
            'title' => __('Improve Site Performance', 'redco-optimizer'),
            'steps' => array(
                __('1. Enable caching with a plugin like WP Rocket or W3 Total Cache', 'redco-optimizer'),
                __('2. Optimize and compress images', 'redco-optimizer'),
                __('3. Minify CSS and JavaScript files', 'redco-optimizer'),
                __('4. Use a Content Delivery Network (CDN)', 'redco-optimizer'),
                __('5. Optimize your database', 'redco-optimizer'),
                __('6. Consider upgrading your hosting plan', 'redco-optimizer')
            ),
            'links' => array(),
            'warning' => ''
        );
    }

    /**
     * Generate image-specific guidance
     */
    private function generate_image_guidance($title, $description) {
        return array(
            'title' => __('Optimize Images', 'redco-optimizer'),
            'steps' => array(
                __('1. Resize images to appropriate dimensions before uploading', 'redco-optimizer'),
                __('2. Use image compression tools or plugins like Smush', 'redco-optimizer'),
                __('3. Convert images to modern formats like WebP', 'redco-optimizer'),
                __('4. Implement lazy loading for images', 'redco-optimizer'),
                __('5. Use a CDN for image delivery', 'redco-optimizer')
            ),
            'links' => array(
                array('text' => __('Media Library', 'redco-optimizer'), 'url' => admin_url('upload.php'))
            ),
            'warning' => ''
        );
    }

    /**
     * Generate CSS/JS assets guidance
     */
    private function generate_assets_guidance($title, $description) {
        $guidance = array(
            'title' => __('Optimize CSS/JavaScript', 'redco-optimizer'),
            'steps' => array(),
            'links' => array(),
            'warning' => __('Test your site thoroughly after making these changes.', 'redco-optimizer')
        );

        if (stripos($description, 'render') !== false && stripos($description, 'blocking') !== false) {
            $guidance['steps'] = array(
                __('1. Identify critical CSS needed for above-the-fold content', 'redco-optimizer'),
                __('2. Inline critical CSS in the document head', 'redco-optimizer'),
                __('3. Load non-critical CSS and JavaScript asynchronously', 'redco-optimizer'),
                __('4. Use defer or async attributes for JavaScript files', 'redco-optimizer')
            );
        } elseif (stripos($description, 'minify') !== false) {
            $guidance['steps'] = array(
                __('1. Use a caching plugin with minification features', 'redco-optimizer'),
                __('2. Remove unused CSS and JavaScript code', 'redco-optimizer'),
                __('3. Combine multiple files when possible', 'redco-optimizer'),
                __('4. Test functionality after minification', 'redco-optimizer')
            );
        } else {
            $guidance['steps'] = array(
                __('1. Minify CSS and JavaScript files', 'redco-optimizer'),
                __('2. Remove unused code and dependencies', 'redco-optimizer'),
                __('3. Optimize file loading order', 'redco-optimizer'),
                __('4. Consider using a performance optimization plugin', 'redco-optimizer')
            );
        }

        return $guidance;
    }

    /**
     * Generate WordPress core guidance
     */
    private function generate_wordpress_guidance($title, $description) {
        return array(
            'title' => __('Update WordPress', 'redco-optimizer'),
            'steps' => array(
                __('1. Backup your website completely before updating', 'redco-optimizer'),
                __('2. Go to Dashboard → Updates', 'redco-optimizer'),
                __('3. Update WordPress core first', 'redco-optimizer'),
                __('4. Update plugins and themes', 'redco-optimizer'),
                __('5. Test your site functionality after updates', 'redco-optimizer')
            ),
            'links' => array(
                array('text' => __('WordPress Updates', 'redco-optimizer'), 'url' => admin_url('update-core.php'))
            ),
            'warning' => __('Always backup before updating WordPress core, plugins, or themes.', 'redco-optimizer')
        );
    }

    /**
     * Generate plugin/theme guidance
     */
    private function generate_plugin_theme_guidance($title, $description) {
        if (stripos($title, 'plugin') !== false) {
            return array(
                'title' => __('Manage Plugins', 'redco-optimizer'),
                'steps' => array(
                    __('1. Go to Plugins → Installed Plugins', 'redco-optimizer'),
                    __('2. Update outdated plugins', 'redco-optimizer'),
                    __('3. Deactivate and remove unused plugins', 'redco-optimizer'),
                    __('4. Test your site after plugin changes', 'redco-optimizer')
                ),
                'links' => array(
                    array('text' => __('Manage Plugins', 'redco-optimizer'), 'url' => admin_url('plugins.php'))
                ),
                'warning' => ''
            );
        } else {
            return array(
                'title' => __('Manage Themes', 'redco-optimizer'),
                'steps' => array(
                    __('1. Go to Appearance → Themes', 'redco-optimizer'),
                    __('2. Update your active theme', 'redco-optimizer'),
                    __('3. Remove unused themes', 'redco-optimizer'),
                    __('4. Backup customizations before theme updates', 'redco-optimizer')
                ),
                'links' => array(
                    array('text' => __('Manage Themes', 'redco-optimizer'), 'url' => admin_url('themes.php'))
                ),
                'warning' => __('Backup theme customizations before updating.', 'redco-optimizer')
            );
        }
    }

    /**
     * Generate security guidance
     */
    private function generate_security_guidance($title, $description) {
        if (stripos($description, 'password') !== false) {
            return array(
                'title' => __('Strengthen Passwords', 'redco-optimizer'),
                'steps' => array(
                    __('1. Go to Users → All Users', 'redco-optimizer'),
                    __('2. Edit user profiles and update passwords', 'redco-optimizer'),
                    __('3. Use strong passwords with 12+ characters', 'redco-optimizer'),
                    __('4. Include uppercase, lowercase, numbers, and symbols', 'redco-optimizer'),
                    __('5. Consider using a password manager', 'redco-optimizer')
                ),
                'links' => array(
                    array('text' => __('Manage Users', 'redco-optimizer'), 'url' => admin_url('users.php'))
                ),
                'warning' => ''
            );
        } else {
            return array(
                'title' => __('Improve Security', 'redco-optimizer'),
                'steps' => array(
                    __('1. Update WordPress, plugins, and themes', 'redco-optimizer'),
                    __('2. Use strong passwords for all user accounts', 'redco-optimizer'),
                    __('3. Install a security plugin like Wordfence', 'redco-optimizer'),
                    __('4. Enable two-factor authentication', 'redco-optimizer'),
                    __('5. Regular security scans and monitoring', 'redco-optimizer')
                ),
                'links' => array(),
                'warning' => __('Security is critical - implement multiple layers of protection.', 'redco-optimizer')
            );
        }
    }

    /**
     * Generate server configuration guidance
     */
    private function generate_server_guidance($title, $description) {
        if (stripos($description, 'php') !== false) {
            return array(
                'title' => __('Update PHP Version', 'redco-optimizer'),
                'steps' => array(
                    __('1. Contact your hosting provider to request PHP update', 'redco-optimizer'),
                    __('2. Test your site on a staging environment first', 'redco-optimizer'),
                    __('3. Update to PHP 8.0 or higher for best performance', 'redco-optimizer'),
                    __('4. Check plugin compatibility before updating', 'redco-optimizer')
                ),
                'links' => array(),
                'warning' => __('Always test PHP updates on a staging site first!', 'redco-optimizer')
            );
        } else {
            return array(
                'title' => __('Optimize Server Configuration', 'redco-optimizer'),
                'steps' => array(
                    __('1. Enable GZIP compression on your server', 'redco-optimizer'),
                    __('2. Configure proper cache headers', 'redco-optimizer'),
                    __('3. Optimize server response times', 'redco-optimizer'),
                    __('4. Contact your hosting provider for assistance', 'redco-optimizer')
                ),
                'links' => array(),
                'warning' => __('Server changes can affect site functionality - test thoroughly.', 'redco-optimizer')
            );
        }
    }

    /**
     * Generate SEO guidance
     */
    private function generate_seo_guidance($title, $description) {
        if (stripos($description, 'meta') !== false) {
            return array(
                'title' => __('Improve Meta Descriptions', 'redco-optimizer'),
                'steps' => array(
                    __('1. Install an SEO plugin like Yoast SEO or RankMath', 'redco-optimizer'),
                    __('2. Edit each page/post to add unique meta descriptions', 'redco-optimizer'),
                    __('3. Keep descriptions between 150-160 characters', 'redco-optimizer'),
                    __('4. Make them compelling and relevant to content', 'redco-optimizer')
                ),
                'links' => array(),
                'warning' => ''
            );
        } elseif (stripos($description, 'alt') !== false) {
            return array(
                'title' => __('Add Alt Text to Images', 'redco-optimizer'),
                'steps' => array(
                    __('1. Go to Media → Library', 'redco-optimizer'),
                    __('2. Edit each image and add descriptive alt text', 'redco-optimizer'),
                    __('3. For new images, add alt text when inserting into posts', 'redco-optimizer'),
                    __('4. Keep alt text descriptive but concise', 'redco-optimizer')
                ),
                'links' => array(
                    array('text' => __('Media Library', 'redco-optimizer'), 'url' => admin_url('upload.php'))
                ),
                'warning' => ''
            );
        } else {
            return array(
                'title' => __('Improve SEO', 'redco-optimizer'),
                'steps' => array(
                    __('1. Install a comprehensive SEO plugin', 'redco-optimizer'),
                    __('2. Optimize page titles and meta descriptions', 'redco-optimizer'),
                    __('3. Add alt text to all images', 'redco-optimizer'),
                    __('4. Create and submit an XML sitemap', 'redco-optimizer'),
                    __('5. Improve site loading speed', 'redco-optimizer')
                ),
                'links' => array(),
                'warning' => ''
            );
        }
    }

    /**
     * Generate category-based guidance fallback
     */
    private function generate_category_guidance($category, $title, $description) {
        switch ($category) {
            case 'wordpress':
                return $this->generate_wordpress_guidance($title, $description);
            case 'database':
                return $this->generate_database_guidance($title, $description);
            case 'performance':
                return $this->generate_performance_guidance($title, $description);
            case 'security':
                return $this->generate_security_guidance($title, $description);
            case 'seo':
                return $this->generate_seo_guidance($title, $description);
            case 'server':
                return $this->generate_server_guidance($title, $description);
            default:
                return array(
                    'title' => __('Manual Fix Required', 'redco-optimizer'),
                    'steps' => array(
                        __('1. Review the issue description carefully', 'redco-optimizer'),
                        __('2. Research the specific problem online', 'redco-optimizer'),
                        __('3. Consider consulting WordPress documentation', 'redco-optimizer'),
                        __('4. Contact your hosting provider if needed', 'redco-optimizer'),
                        __('5. Consider hiring a WordPress developer for complex issues', 'redco-optimizer')
                    ),
                    'links' => array(
                        array('text' => __('WordPress Support', 'redco-optimizer'), 'url' => 'https://wordpress.org/support/')
                    ),
                    'warning' => ''
                );
        }
    }

    /**
     * Format guidance into HTML
     */
    private function format_guidance_html($guidance) {
        $html = '<h5><span class="dashicons dashicons-lightbulb"></span>' . esc_html($guidance['title']) . '</h5>';

        if (!empty($guidance['warning'])) {
            $html .= '<div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 8px 12px; margin: 8px 0; border-radius: 0 4px 4px 0;">';
            $html .= '<strong>' . __('⚠️ Warning:', 'redco-optimizer') . '</strong> ' . esc_html($guidance['warning']);
            $html .= '</div>';
        }

        if (!empty($guidance['steps'])) {
            $html .= '<ol>';
            foreach ($guidance['steps'] as $step) {
                $html .= '<li>' . esc_html($step) . '</li>';
            }
            $html .= '</ol>';
        }

        if (!empty($guidance['links'])) {
            $html .= '<p><strong>' . __('Helpful Links:', 'redco-optimizer') . '</strong></p>';
            $html .= '<ul>';
            foreach ($guidance['links'] as $link) {
                $html .= '<li><a href="' . esc_url($link['url']) . '" target="_blank">' . esc_html($link['text']) . '</a></li>';
            }
            $html .= '</ul>';
        }

        return $html;
    }

    /**
     * AJAX handler for exporting diagnostic report
     */
    public function ajax_export_diagnostic_report() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $last_scan = get_option('redco_diagnostic_results', array());
        $fix_history = get_option('redco_diagnostic_fix_history', array());

        $report = array(
            'site_info' => array(
                'url' => home_url(),
                'wordpress_version' => get_bloginfo('version'),
                'php_version' => PHP_VERSION,
                'theme' => wp_get_theme()->get('Name'),
                'active_plugins' => count(get_option('active_plugins', array()))
            ),
            'scan_results' => $last_scan,
            'fix_history' => $fix_history,
            'generated_at' => current_time('mysql'),
            'generated_by' => 'Redco Optimizer Diagnostic Module'
        );

        // Set headers for file download
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="redco-diagnostic-report-' . date('Y-m-d-H-i-s') . '.json"');
        header('Content-Length: ' . strlen(json_encode($report, JSON_PRETTY_PRINT)));

        echo json_encode($report, JSON_PRETTY_PRINT);
        exit;
    }

    /**
     * Run scheduled scan
     */
    public function run_scheduled_scan() {
        if ($this->settings['auto_scan_frequency'] === 'disabled') {
            return;
        }

        $results = $this->run_diagnostic_scan('comprehensive', !empty($this->settings['pagespeed_api_key']));

        // Check if emergency mode should be triggered
        if (isset($results['pagespeed_score']) && $results['pagespeed_score'] < $this->settings['emergency_mode_threshold']) {
            $this->activate_emergency_mode();
        }

        // Send notification email if critical issues found
        if ($results['critical_issues'] > 0) {
            $this->send_notification_email($results);
        }

        // Auto-fix if enabled
        if ($this->settings['auto_fix_enabled'] && !empty($results['issues'])) {
            $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();
            $autofix_engine->apply_auto_fixes($results['issues'], $this->settings['backup_before_fix']);
        }
    }

    /**
     * Show critical issue notices - REMOVED to prevent layout shifts
     * Critical issues are now displayed within the module interface only
     */
    public function show_critical_issue_notices() {
        // Method disabled - no admin notices to prevent layout stability issues
        return;
    }

    /**
     * Activate emergency mode
     */
    private function activate_emergency_mode() {
        // Disable heavy modules temporarily
        $heavy_modules = array('critical-resource-optimizer', 'database-cleanup');
        $options = get_option('redco_optimizer_options', array());

        if (!isset($options['emergency_backup'])) {
            $options['emergency_backup'] = $options['modules_enabled'] ?? array();
        }

        $options['modules_enabled'] = array_diff($options['modules_enabled'] ?? array(), $heavy_modules);
        update_option('redco_optimizer_options', $options);

        // Set emergency mode flag
        update_option('redco_emergency_mode_active', true);

        // Clear all caches
        wp_cache_flush();
    }

    /**
     * Deactivate emergency mode
     */
    private function deactivate_emergency_mode() {
        $options = get_option('redco_optimizer_options', array());

        // Restore original modules if backup exists
        if (isset($options['emergency_backup'])) {
            $options['modules_enabled'] = $options['emergency_backup'];
            unset($options['emergency_backup']);
            update_option('redco_optimizer_options', $options);
        }

        // Remove emergency mode flag
        delete_option('redco_emergency_mode_active');
    }

    /**
     * Send notification email
     */
    private function send_notification_email($results) {
        $email = $this->settings['notification_email'];
        if (empty($email)) {
            return;
        }

        $subject = sprintf(__('[%s] Critical Performance Issues Detected', 'redco-optimizer'), get_bloginfo('name'));

        $message = sprintf(__('Critical performance issues have been detected on your website %s.', 'redco-optimizer'), home_url()) . "\n\n";
        $message .= sprintf(__('Health Score: %d%%', 'redco-optimizer'), $results['health_score']) . "\n";
        $message .= sprintf(__('Performance Score: %d%%', 'redco-optimizer'), $results['performance_score']) . "\n";
        $message .= sprintf(__('Critical Issues: %d', 'redco-optimizer'), $results['critical_issues']) . "\n\n";

        $message .= __('Please log in to your WordPress admin to review and fix these issues.', 'redco-optimizer') . "\n";
        $message .= admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "\n\n";

        $message .= __('This email was sent by Redco Optimizer Diagnostic Module.', 'redco-optimizer');

        wp_mail($email, $subject, $message);
    }

    /**
     * CRITICAL FIX: Migrate existing fix sessions to ensure rollback IDs are present - ENHANCED
     */
    private function migrate_fix_sessions_rollback_ids() {
        $fix_history = get_option('redco_diagnostic_fix_history', array());
        $updated = false;
        $migration_count = 0;



        foreach ($fix_history as &$session) {
            $session_timestamp = isset($session['timestamp']) ? $session['timestamp'] : time();
            $has_backup_created = isset($session['backup_created']) && $session['backup_created'];
            $has_rollback_id = isset($session['rollback_id']) && !empty($session['rollback_id']);

            error_log("REDCO MIGRATION: Processing session - timestamp: {$session_timestamp}, backup_created: " . ($has_backup_created ? 'true' : 'false') . ", has_rollback_id: " . ($has_rollback_id ? 'true' : 'false'));

            // If session has backup_created but no rollback_id, try to find/create one
            if ($has_backup_created && !$has_rollback_id) {
                $found_rollback_id = null;

                // Strategy 1: Try to find backup_id in session details
                if (isset($session['details']) && is_array($session['details'])) {
                    foreach ($session['details'] as $detail) {
                        if (isset($detail['rollback_id']) && !empty($detail['rollback_id'])) {
                            $found_rollback_id = $detail['rollback_id'];

                            break;
                        }
                        // Also check for backup_id field
                        if (isset($detail['backup_id']) && !empty($detail['backup_id'])) {
                            $found_rollback_id = $detail['backup_id'];

                            break;
                        }
                    }
                }

                // Strategy 2: Check if there's a backup_id field at session level
                if (!$found_rollback_id && isset($session['backup_id']) && !empty($session['backup_id'])) {
                    $found_rollback_id = $session['backup_id'];
        
                }

                // Strategy 3: Generate a rollback_id based on timestamp and validate it exists
                if (!$found_rollback_id) {
                    // Try common backup ID formats
                    $possible_ids = array(
                        'backup_' . date('Y-m-d_H-i-s', $session_timestamp) . '_' . substr(md5($session_timestamp), 0, 8),
                        'backup_' . $session_timestamp . '_migrated',
                        'backup_' . date('Y-m-d_H-i-s', $session_timestamp),
                        'backup_' . $session_timestamp
                    );

                    foreach ($possible_ids as $test_id) {
                        if ($this->validate_backup_exists($test_id)) {
                            $found_rollback_id = $test_id;

                            break;
                        }
                    }

                    // If no existing backup found, create a migrated rollback ID
                    if (!$found_rollback_id) {
                        $found_rollback_id = 'backup_' . $session_timestamp . '_migrated';
                        error_log("REDCO MIGRATION: Generated migrated rollback ID: {$found_rollback_id}");
                    }
                }

                // Assign the found/generated rollback_id
                if ($found_rollback_id) {
                    $session['rollback_id'] = $found_rollback_id;
                    $updated = true;
                    $migration_count++;

                }
            }
        }

        if ($updated) {
            update_option('redco_diagnostic_fix_history', $fix_history);

        } else {

        }

        return $migration_count;
    }

    /**
     * CRITICAL FIX: Validate that a backup exists for rollback - ENHANCED with correct paths
     */
    private function validate_backup_exists($backup_id) {
        if (empty($backup_id)) {
            return false;
        }

        // Check for backup data in options table (most common storage method)
        $backup_option_key = 'redco_backup_' . $backup_id;
        $backup_data = get_option($backup_option_key);
        if (!empty($backup_data)) {
            error_log("REDCO VALIDATION: Found backup in options table: {$backup_option_key}");
            return true;
        }

        // Check for optimization backup (alternative storage method)
        $optimization_backup_key = 'redco_optimization_backup_' . $backup_id;
        $optimization_backup = get_option($optimization_backup_key);
        if (!empty($optimization_backup)) {
            error_log("REDCO VALIDATION: Found optimization backup: {$optimization_backup_key}");
            return true;
        }

        // Check for diagnostic backup (another alternative)
        $diagnostic_backup_key = 'redco_diagnostic_backup_' . $backup_id;
        $diagnostic_backup = get_option($diagnostic_backup_key);
        if (!empty($diagnostic_backup)) {
            error_log("REDCO VALIDATION: Found diagnostic backup: {$diagnostic_backup_key}");
            return true;
        }

        // CRITICAL FIX: Use the same backup directory logic as AutoFix Engine
        $backup_dirs = $this->get_possible_backup_directories();

        foreach ($backup_dirs as $backup_dir) {
            if (!is_dir($backup_dir)) {
                continue;
            }

            // Check for backup directory (AutoFix Engine creates subdirectories) with proper path handling
            $backup_subdir = wp_normalize_path($backup_dir . $backup_id . DIRECTORY_SEPARATOR);
            if (is_dir($backup_subdir)) {
                // Check for backup metadata file
                $metadata_file = wp_normalize_path($backup_subdir . 'backup_data.json');
                if (file_exists($metadata_file)) {
                    error_log("REDCO VALIDATION: Found backup directory: {$backup_subdir}");
                    return true;
                }
            }

            // Check for backup file (alternative format) with proper path handling
            $backup_file = wp_normalize_path($backup_dir . $backup_id . '.json');
            if (file_exists($backup_file)) {
                error_log("REDCO VALIDATION: Found backup file: {$backup_file}");
                return true;
            }
        }


        return false;
    }

    /**
     * PRODUCTION-READY: Get all possible backup directory paths with proper WordPress path handling
     */
    private function get_possible_backup_directories() {
        $backup_dirs = array();

        // Priority 1: Plugin-specific backup directory (if available)
        if (function_exists('redco_diagnostic_get_cache_dir')) {
            $backup_dir = redco_diagnostic_get_cache_dir('backup');
            if (!empty($backup_dir)) {
                $backup_dirs[] = wp_normalize_path(trailingslashit($backup_dir));
            }
        }

        // Priority 2: General plugin backup directory (if available)
        if (function_exists('redco_get_cache_dir')) {
            $backup_dir = redco_get_cache_dir('backup');
            if (!empty($backup_dir)) {
                $backup_dirs[] = wp_normalize_path(trailingslashit($backup_dir));
            }
        }

        // Priority 3: WordPress uploads directory (most reliable)
        $upload_dir = wp_upload_dir();
        if (!empty($upload_dir['basedir']) && !$upload_dir['error']) {
            $backup_dirs[] = wp_normalize_path($upload_dir['basedir'] . DIRECTORY_SEPARATOR . 'redco-diagnostic' . DIRECTORY_SEPARATOR . 'backups');
        }

        // Priority 4: WP Content directory
        if (defined('WP_CONTENT_DIR') && is_dir(WP_CONTENT_DIR)) {
            $backup_dirs[] = wp_normalize_path(WP_CONTENT_DIR . DIRECTORY_SEPARATOR . 'redco-backups');
        }

        // Priority 5: Alternative uploads locations (CRITICAL: Must match engine candidates)
        if (!empty($upload_dir['basedir']) && !$upload_dir['error']) {
            // This matches the engine's Priority 5 alternative location
            $backup_dirs[] = wp_normalize_path($upload_dir['basedir'] . DIRECTORY_SEPARATOR . 'redco-cachediagnostic-backups');
            $backup_dirs[] = wp_normalize_path($upload_dir['basedir'] . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'redco-diagnostic' . DIRECTORY_SEPARATOR . 'backups');
        }

        // Ensure all paths end with proper separator and remove duplicates
        $normalized_dirs = array();
        foreach ($backup_dirs as $dir) {
            $normalized_dirs[] = trailingslashit($dir);
        }

        return array_unique($normalized_dirs);
    }

    /**
     * AJAX handler for loading recent fixes
     */
    public function ajax_load_recent_fixes() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        // CRITICAL FIX: Migrate existing sessions to ensure rollback IDs are present
        $this->migrate_fix_sessions_rollback_ids();

        $fix_history = get_option('redco_diagnostic_fix_history', array());

        // REMOVED: Sample data for testing - now shows empty state when no fixes exist

        $recent_fixes = array_slice(array_reverse($fix_history), 0, 5);

        $html = '';
        if (!empty($recent_fixes)) {
            foreach ($recent_fixes as $fix_session) {
                $html .= '<div class="fix-item">';
                $html .= '<div class="fix-header">';
                $html .= '<div class="fix-date">' . human_time_diff($fix_session['timestamp']) . ' ' . __('ago', 'redco-optimizer') . '</div>';
                $html .= '<div class="fix-count">' . sprintf(__('%d fixes', 'redco-optimizer'), $fix_session['fixes_applied']) . '</div>';
                $html .= '</div>';

                // CRITICAL FIX: Enhanced rollback button visibility logic with comprehensive validation
                $has_backup = isset($fix_session['backup_created']) && $fix_session['backup_created'];
                $has_rollback_id = isset($fix_session['rollback_id']) && !empty($fix_session['rollback_id']);

                // ENHANCED: Try multiple strategies to find rollback ID
                $rollback_id_to_use = '';

                if ($has_rollback_id) {
                    $rollback_id_to_use = $fix_session['rollback_id'];
                } elseif (isset($fix_session['backup_id']) && !empty($fix_session['backup_id'])) {
                    // Strategy 1: Use backup_id as rollback_id
                    $rollback_id_to_use = $fix_session['backup_id'];
                    $has_rollback_id = true;
                    error_log("REDCO ROLLBACK: Using backup_id as rollback_id: {$rollback_id_to_use}");
                } elseif (isset($fix_session['details']) && is_array($fix_session['details'])) {
                    // Strategy 2: Look for rollback_id in fix details
                    foreach ($fix_session['details'] as $detail) {
                        if (isset($detail['rollback_id']) && !empty($detail['rollback_id'])) {
                            $rollback_id_to_use = $detail['rollback_id'];
                            $has_rollback_id = true;
                            error_log("REDCO ROLLBACK: Found rollback_id in details: {$rollback_id_to_use}");
                            break;
                        }
                    }
                }

                // ENHANCED: Validate that backup actually exists if we have an ID
                $backup_exists = false;
                if ($has_rollback_id && !empty($rollback_id_to_use)) {
                    $backup_exists = $this->validate_backup_exists($rollback_id_to_use);
                    if (!$backup_exists) {
                        error_log("REDCO ROLLBACK: Backup validation failed for ID: {$rollback_id_to_use}");

                        // FALLBACK: For migrated sessions, assume backup exists if backup_created is true
                        if ($has_backup && strpos($rollback_id_to_use, '_migrated') !== false) {
                            $backup_exists = true;
                            error_log("REDCO ROLLBACK: Using fallback validation for migrated session: {$rollback_id_to_use}");
                        }
                    } else {
                        error_log("REDCO ROLLBACK: Backup validation successful for ID: {$rollback_id_to_use}");
                    }
                }

                // CRITICAL FIX: More flexible rollback capability detection
                $confirmed_rollback_capability = false;
                $display_backup_info = false;
                $backup_status_text = '';

                // Strategy 1: Full rollback capability (backup exists and validated)
                if ($has_rollback_id && $backup_exists && !empty($rollback_id_to_use)) {
                    $confirmed_rollback_capability = true;
                    $display_backup_info = true;
                    $backup_status_text = __('Backup created', 'redco-optimizer');
                    error_log("REDCO DISPLAY: Full rollback capability confirmed for {$rollback_id_to_use}");
                }
                // Strategy 2: Backup created flag is set (show backup info even if validation fails)
                elseif ($has_backup) {
                    $display_backup_info = true;
                    $backup_status_text = __('Backup created', 'redco-optimizer');

                    // Try to enable rollback if we can find a valid ID
                    if ($has_rollback_id && $backup_exists && !empty($rollback_id_to_use)) {
                        $confirmed_rollback_capability = true;
                    }
                    error_log("REDCO DISPLAY: Backup info shown based on backup_created flag");
                }
                // Strategy 3: Rollback ID exists (show as backup available)
                elseif ($has_rollback_id && !empty($rollback_id_to_use)) {
                    $display_backup_info = true;
                    $backup_status_text = __('Backup available', 'redco-optimizer');

                    // Enable rollback if backup validates
                    if ($backup_exists) {
                        $confirmed_rollback_capability = true;
                    }
                    error_log("REDCO DISPLAY: Backup info shown based on rollback_id existence");
                }
                // Strategy 4: Legacy fix sessions (show basic info)
                else {
                    $display_backup_info = true;
                    $backup_status_text = __('Fix applied', 'redco-optimizer');
                    error_log("REDCO DISPLAY: Legacy fix session - showing basic info");
                }

                if ($display_backup_info) {
                    $html .= '<div class="fix-backup">';
                    $html .= '<span class="dashicons dashicons-backup"></span>';
                    $html .= '<span>' . $backup_status_text . '</span>';

                    // CRITICAL FIX: Only show rollback button if we have confirmed rollback capability
                    if ($confirmed_rollback_capability && !empty($rollback_id_to_use)) {
                        $html .= '<button type="button" class="button-link rollback-fix" data-backup-id="' . esc_attr($rollback_id_to_use) . '" title="' . esc_attr(__('Undo these fixes and restore previous state', 'redco-optimizer')) . '">';
                        $html .= '<span class="dashicons dashicons-undo"></span>';
                        $html .= __('Rollback', 'redco-optimizer');
                        $html .= '</button>';
                        error_log("REDCO DISPLAY: Rollback button added for {$rollback_id_to_use}");
                    } else {
                        error_log("REDCO DISPLAY: No rollback button - capability: " . ($confirmed_rollback_capability ? 'true' : 'false') . ", ID: {$rollback_id_to_use}");
                    }

                    $html .= '</div>';
                } else {

                }
                $html .= '</div>';
            }
        } else {
            $html = '<div class="no-fixes-message"><p>' . __('No recent fixes found.', 'redco-optimizer') . '</p></div>';
        }

        wp_send_json_success(array(
            'html' => $html,
            'count' => count($recent_fixes)
        ));
    }



    /**
     * Check if an issue is still present on the site
     */
    private function is_issue_still_present($issue) {
        $issue_id = $issue['id'] ?? '';
        $category = $issue['category'] ?? 'general';

        // Check based on issue type and category
        switch ($category) {
            case 'wordpress':
                return $this->check_wordpress_issue($issue_id, $issue);

            case 'database':
                return $this->check_database_issue($issue_id, $issue);

            case 'server':
                return $this->check_server_issue($issue_id, $issue);

            case 'security':
                return $this->check_security_issue($issue_id, $issue);

            case 'modules':
                return $this->check_module_issue($issue_id, $issue);

            case 'frontend':
                return $this->check_frontend_issue($issue_id, $issue);

            default:
                // For unknown categories, assume issue is still present
                return true;
        }
    }

    /**
     * Check WordPress-specific issues
     */
    private function check_wordpress_issue($issue_id, $issue) {
        switch ($issue_id) {
            case 'outdated_wordpress':
                $wp_version = get_bloginfo('version');
                $latest_version = $this->get_latest_wordpress_version();
                return version_compare($wp_version, $latest_version, '<');

            case 'debug_enabled_production':
                return (defined('WP_DEBUG') && WP_DEBUG && !$this->is_development_environment());

            default:
                return true; // Assume still present if we can't check
        }
    }

    /**
     * Check database-specific issues
     */
    private function check_database_issue($issue_id, $issue) {
        switch ($issue_id) {
            case 'large_database':
                $db_size = $this->get_database_size();
                return $db_size > 100 * 1024 * 1024; // 100MB threshold

            case 'large_autoload':
                $autoload_size = $this->get_autoload_size();
                return $autoload_size > 1024 * 1024; // 1MB threshold

            default:
                return true;
        }
    }

    /**
     * Check server-specific issues (FIXED: Use comprehensive detection)
     */
    private function check_server_issue($issue_id, $issue) {
        switch ($issue_id) {
            case 'no_compression':
                return !$this->is_compression_enabled_comprehensive();

            case 'missing_cache_headers':
                return !$this->has_proper_cache_headers();

            case 'outdated_php':
                $php_version = PHP_VERSION;
                return version_compare($php_version, '8.0', '<');

            default:
                return true;
        }
    }

    /**
     * Check security-specific issues
     */
    private function check_security_issue($issue_id, $issue) {
        switch ($issue_id) {
            case 'no_ssl':
                return !is_ssl();

            default:
                return true;
        }
    }

    /**
     * Check module-specific issues
     */
    private function check_module_issue($issue_id, $issue) {
        if (strpos($issue_id, 'disabled_module_') === 0) {
            $module_name = str_replace('disabled_module_', '', $issue_id);
            return !redco_is_module_enabled($module_name);
        }

        return true;
    }

    /**
     * Check frontend-specific issues
     */
    private function check_frontend_issue($issue_id, $issue) {
        // Handle render-blocking issues
        if (strpos($issue_id, 'render_blocking_') === 0) {
            // Check if render blocking optimization is enabled and working
            $optimization_settings = get_option('redco_render_blocking_optimization');
            if ($optimization_settings && isset($optimization_settings['enabled']) && $optimization_settings['enabled']) {
                // Check if .htaccess rules are present
                $htaccess_file = ABSPATH . '.htaccess';
                if (file_exists($htaccess_file)) {
                    $content = file_get_contents($htaccess_file);
                    if (strpos($content, 'Render Blocking Resource Optimization') !== false) {
                        // Optimization is properly configured, issue is resolved
                        return false;
                    }
                }
            }

            // If optimization is not properly configured, re-check the specific resource
            if (isset($issue['description'])) {
                // Extract resource URL from description if possible
                $resource_url = $this->extract_resource_url_from_description($issue['description']);
                if ($resource_url) {
                    return !$this->is_resource_optimized($resource_url, 'js'); // Assume JS for now
                }
            }

            // Fallback: assume issue is still present if we can't verify
            return true;
        }

        // Handle large DOM size issues
        if ($issue_id === 'large_dom_size') {
            $dom_size = $this->get_average_dom_size();
            return $dom_size > 1500;
        }

        // Handle heavy scripts issues
        if ($issue_id === 'heavy_scripts') {
            return $this->has_heavy_scripts();
        }

        // Handle layout shift issues
        if ($issue_id === 'layout_shift_issues') {
            return $this->has_layout_shift_issues();
        }

        // Handle jQuery in header issues
        if ($issue_id === 'jquery_in_header') {
            return $this->theme_loads_jquery_in_header();
        }

        // For unknown frontend issues, assume still present
        return true;
    }

    /**
     * Extract resource URL from issue description
     */
    private function extract_resource_url_from_description($description) {
        // Try to extract URL from description like "Js is blocking page rendering: view.min.js"
        if (preg_match('/rendering:\s*(.+)$/', $description, $matches)) {
            return trim($matches[1]);
        }
        return null;
    }

    /**
     * Check if a resource is already optimized (delegate to helpers)
     */
    private function is_resource_optimized($url, $type) {
        // Use the helper class method if available
        if (method_exists($this, 'detect_render_blocking_resources')) {
            // Check if render blocking optimization is enabled
            $optimization_settings = get_option('redco_render_blocking_optimization');
            if (!$optimization_settings || !isset($optimization_settings['enabled']) || !$optimization_settings['enabled']) {
                return false;
            }

            // Check if this is a WordPress core resource that should be optimized
            $wp_includes_url = includes_url();
            $wp_content_url = content_url();

            if (strpos($url, $wp_includes_url) !== false || strpos($url, $wp_content_url) !== false) {
                // This is a WordPress resource that should be optimized
                return true;
            }

            // Check if this is a theme/plugin resource
            $theme_url = get_template_directory_uri();
            if (strpos($url, $theme_url) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * NEW: AJAX handler for scanning real optimization opportunities
     */
    public function ajax_scan_optimization_opportunities() {
        // Verify nonce and permissions
        if (!check_ajax_referer('redco_diagnostic_nonce', 'nonce', false) || !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Security check failed', 'redco-optimizer')));
            return;
        }

        try {
            $opportunities = $this->scan_real_optimization_opportunities();

            wp_send_json_success(array(
                'opportunities' => $opportunities,
                'message' => __('Optimization opportunities scanned successfully', 'redco-optimizer')
            ));
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        } catch (Error $e) {
            wp_send_json_error(array(
                'message' => 'Fatal error: ' . $e->getMessage()
            ));
        }
    }

    /**
     * NEW: AJAX handler for applying real optimization fixes
     */
    public function ajax_apply_optimization_fix() {
        // Verify nonce and permissions
        if (!check_ajax_referer('redco_diagnostic_nonce', 'nonce', false) || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        $opportunity_id = sanitize_text_field($_POST['opportunity_id'] ?? '');
        $category = sanitize_text_field($_POST['category'] ?? '');
        $title = sanitize_text_field($_POST['title'] ?? '');

        if (empty($opportunity_id) || empty($category)) {
            wp_send_json_error(array(
                'message' => __('Invalid optimization parameters', 'redco-optimizer')
            ));
        }

        try {
            $result = $this->apply_real_optimization_fix($opportunity_id, $category, $title);

            if ($result['success']) {
                wp_send_json_success($result);
            } else {
                wp_send_json_error($result);
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => $e->getMessage()
            ));
        }
    }

    /**
     * NEW: AJAX handler for applying all optimization fixes
     */
    public function ajax_apply_all_optimization_fixes() {
        // Verify nonce and permissions
        if (!check_ajax_referer('redco_diagnostic_nonce', 'nonce', false) || !current_user_can('manage_options')) {
            wp_die(__('Security check failed', 'redco-optimizer'));
        }

        $opportunities_json = sanitize_textarea_field($_POST['opportunities'] ?? '');
        $opportunities = json_decode($opportunities_json, true);

        if (empty($opportunities) || !is_array($opportunities)) {
            wp_send_json_error(array(
                'message' => __('No opportunities provided', 'redco-optimizer')
            ));
        }

        $results = array(
            'applied_count' => 0,
            'failed_count' => 0,
            'details' => array()
        );

        foreach ($opportunities as $opportunity) {
            if (!isset($opportunity['id']) || !isset($opportunity['category'])) {
                continue;
            }

            try {
                $result = $this->apply_real_optimization_fix(
                    $opportunity['id'],
                    $opportunity['category'],
                    $opportunity['title'] ?? ''
                );

                if ($result['success']) {
                    $results['applied_count']++;
                    $results['details'][] = array(
                        'id' => $opportunity['id'],
                        'status' => 'success',
                        'message' => $result['message']
                    );
                } else {
                    $results['failed_count']++;
                    $results['details'][] = array(
                        'id' => $opportunity['id'],
                        'status' => 'failed',
                        'message' => $result['message']
                    );
                }
            } catch (Exception $e) {
                $results['failed_count']++;
                $results['details'][] = array(
                    'id' => $opportunity['id'],
                    'status' => 'error',
                    'message' => $e->getMessage()
                );
            }
        }

        if ($results['applied_count'] > 0) {
            wp_send_json_success($results);
        } else {
            wp_send_json_error(array(
                'message' => __('No optimizations could be applied', 'redco-optimizer'),
                'details' => $results['details']
            ));
        }
    }

    /**
     * NEW: Scan for real optimization opportunities
     */
    private function scan_real_optimization_opportunities() {
        $opportunities = array(
            'performance' => array(),
            'security' => array(),
            'seo' => array(),
            'maintenance' => array()
        );

        // PERFORMANCE OPTIMIZATIONS

        // Check if browser caching is enabled
        if (!$this->is_browser_caching_enabled()) {
            $opportunities['performance'][] = array(
                'id' => 'enable_browser_caching',
                'title' => __('Enable Browser Caching', 'redco-optimizer'),
                'description' => __('Configure browser caching headers to improve repeat visitor performance', 'redco-optimizer'),
                'impact' => 'high',
                'icon' => 'dashicons-clock',
                'fixable' => true
            );
        }

        // Check if GZIP compression is enabled (FIXED: Use comprehensive detection)
        if (!$this->is_compression_enabled_comprehensive()) {
            $opportunities['performance'][] = array(
                'id' => 'enable_gzip_compression',
                'title' => __('Enable GZIP Compression', 'redco-optimizer'),
                'description' => __('Reduce file sizes with server-side compression', 'redco-optimizer'),
                'impact' => 'high',
                'icon' => 'dashicons-archive',
                'fixable' => true
            );
        }

        // Check for large autoload data
        $autoload_size = $this->get_autoload_size();
        if ($autoload_size > 1024 * 1024) { // 1MB
            $opportunities['performance'][] = array(
                'id' => 'optimize_autoload',
                'title' => __('Optimize Database Autoload', 'redco-optimizer'),
                'description' => sprintf(__('Autoload data is %s. Optimize for faster page loads.', 'redco-optimizer'), redco_format_bytes($autoload_size)),
                'impact' => 'medium',
                'icon' => 'dashicons-database',
                'fixable' => true
            );
        }

        // SECURITY OPTIMIZATIONS

        // Check WordPress version
        if ($this->needs_wordpress_update()) {
            $opportunities['security'][] = array(
                'id' => 'update_wordpress',
                'title' => __('Update WordPress Core', 'redco-optimizer'),
                'description' => __('Update to the latest WordPress version for security patches', 'redco-optimizer'),
                'impact' => 'high',
                'icon' => 'dashicons-shield',
                'fixable' => false // Manual update required
            );
        }

        // Check for security headers
        if (!$this->has_security_headers()) {
            $opportunities['security'][] = array(
                'id' => 'add_security_headers',
                'title' => __('Add Security Headers', 'redco-optimizer'),
                'description' => __('Add security headers to protect against common attacks', 'redco-optimizer'),
                'impact' => 'medium',
                'icon' => 'dashicons-lock',
                'fixable' => true
            );
        }

        // SEO OPTIMIZATIONS

        // Check for missing meta descriptions
        if ($this->has_missing_meta_descriptions()) {
            $opportunities['seo'][] = array(
                'id' => 'add_meta_descriptions',
                'title' => __('Add Meta Descriptions', 'redco-optimizer'),
                'description' => __('Improve search engine visibility with meta descriptions', 'redco-optimizer'),
                'impact' => 'medium',
                'icon' => 'dashicons-search',
                'fixable' => false // Requires manual content creation
            );
        }

        // MAINTENANCE OPTIMIZATIONS

        // Check for database cleanup opportunities
        $cleanup_data = $this->get_database_cleanup_opportunities();
        if (!empty($cleanup_data)) {
            $opportunities['maintenance'][] = array(
                'id' => 'cleanup_database',
                'title' => __('Clean Database', 'redco-optimizer'),
                'description' => sprintf(__('Remove %d unnecessary database entries to improve performance', 'redco-optimizer'), $cleanup_data['count']),
                'impact' => 'low',
                'icon' => 'dashicons-database',
                'fixable' => true
            );
        }

        // Check for plugin updates
        $plugin_updates = $this->get_plugin_update_count();
        if ($plugin_updates > 0) {
            $opportunities['maintenance'][] = array(
                'id' => 'update_plugins',
                'title' => __('Update Plugins', 'redco-optimizer'),
                'description' => sprintf(__('%d plugins have updates available', 'redco-optimizer'), $plugin_updates),
                'impact' => 'medium',
                'icon' => 'dashicons-admin-plugins',
                'fixable' => false // Manual update recommended
            );
        }

        return $opportunities;
    }

    /**
     * NEW: Count total optimization opportunities
     */
    private function count_total_opportunities($opportunities) {
        $total = 0;
        foreach ($opportunities as $category => $category_opportunities) {
            $total += count($category_opportunities);
        }
        return $total;
    }

    /**
     * NEW: Count auto-fixable optimization opportunities
     */
    private function count_auto_fixable_opportunities($opportunities) {
        $auto_fixable = 0;
        foreach ($opportunities as $category => $category_opportunities) {
            foreach ($category_opportunities as $opportunity) {
                if (isset($opportunity['fixable']) && $opportunity['fixable']) {
                    $auto_fixable++;
                }
            }
        }
        return $auto_fixable;
    }

    /**
     * NEW: Apply real optimization fix
     */
    private function apply_real_optimization_fix($opportunity_id, $category, $title) {
        $result = array(
            'success' => false,
            'message' => '',
            'changes_made' => array()
        );

        // Create backup before making changes
        $backup_id = $this->create_optimization_backup($opportunity_id);

        try {
            switch ($opportunity_id) {
                case 'enable_browser_caching':
                    $result = $this->fix_enable_browser_caching();
                    break;

                case 'enable_gzip_compression':
                    $result = $this->fix_enable_gzip_compression();
                    break;

                case 'optimize_autoload':
                    $result = $this->fix_optimize_autoload();
                    break;

                case 'add_security_headers':
                    $result = $this->fix_add_security_headers();
                    break;

                case 'cleanup_database':
                    $result = $this->fix_cleanup_database();
                    break;

                default:
                    $result['message'] = __('Optimization not implemented yet', 'redco-optimizer');
                    break;
            }

            // Log the fix if successful
            if ($result['success']) {
                $this->log_optimization_fix($opportunity_id, $title, $result['changes_made'], $backup_id);
            }

        } catch (Exception $e) {
            $result['success'] = false;
            $result['message'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * NEW: Enable browser caching via .htaccess
     */
    private function fix_enable_browser_caching() {
        $result = array('success' => false, 'message' => '', 'changes_made' => array());

        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file) && file_exists($htaccess_file)) {
            $result['message'] = __('.htaccess file is not writable', 'redco-optimizer');
            return $result;
        }

        $caching_rules = "
# BEGIN Redco Optimizer Browser Caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css \"access plus 1 year\"
    ExpiresByType application/javascript \"access plus 1 year\"
    ExpiresByType text/javascript \"access plus 1 year\"
    ExpiresByType image/png \"access plus 1 year\"
    ExpiresByType image/jpg \"access plus 1 year\"
    ExpiresByType image/jpeg \"access plus 1 year\"
    ExpiresByType image/gif \"access plus 1 year\"
    ExpiresByType image/svg+xml \"access plus 1 year\"
    ExpiresByType image/webp \"access plus 1 year\"
    ExpiresByType font/woff \"access plus 1 year\"
    ExpiresByType font/woff2 \"access plus 1 year\"
</IfModule>

<IfModule mod_headers.c>
    <FilesMatch \"\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2)$\">
        Header set Cache-Control \"public, max-age=31536000\"
    </FilesMatch>
</IfModule>
# END Redco Optimizer Browser Caching

";

        $current_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';

        // Check if rules already exist
        if (strpos($current_content, 'BEGIN Redco Optimizer Browser Caching') !== false) {
            $result['message'] = __('Browser caching rules already exist', 'redco-optimizer');
            $result['success'] = true;
            return $result;
        }

        // Add rules to .htaccess
        $new_content = $caching_rules . $current_content;

        if (file_put_contents($htaccess_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = __('Browser caching enabled successfully', 'redco-optimizer');
            $result['changes_made'][] = 'Added browser caching rules to .htaccess';
        } else {
            $result['message'] = __('Failed to write to .htaccess file', 'redco-optimizer');
        }

        return $result;
    }

    /**
     * NEW: Enable GZIP compression via .htaccess
     */
    private function fix_enable_gzip_compression() {
        $result = array('success' => false, 'message' => '', 'changes_made' => array());

        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file) && file_exists($htaccess_file)) {
            $result['message'] = __('.htaccess file is not writable', 'redco-optimizer');
            return $result;
        }

        $compression_rules = "
# BEGIN Redco Optimizer GZIP Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>
# END Redco Optimizer GZIP Compression

";

        $current_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';

        // Check if rules already exist
        if (strpos($current_content, 'BEGIN Redco Optimizer GZIP Compression') !== false) {
            $result['message'] = __('GZIP compression rules already exist', 'redco-optimizer');
            $result['success'] = true;
            return $result;
        }

        // Add rules to .htaccess
        $new_content = $compression_rules . $current_content;

        if (file_put_contents($htaccess_file, $new_content)) {
            $result['success'] = true;
            $result['message'] = __('GZIP compression enabled successfully', 'redco-optimizer');
            $result['changes_made'][] = 'Added GZIP compression rules to .htaccess';
        } else {
            $result['message'] = __('Failed to write to .htaccess file', 'redco-optimizer');
        }

        return $result;
    }

    /**
     * NEW: Helper methods for checking optimization status
     */
    private function is_browser_caching_enabled() {
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            return false;
        }

        $content = file_get_contents($htaccess_file);
        return strpos($content, 'mod_expires') !== false || strpos($content, 'Cache-Control') !== false;
    }

    /**
     * COMPREHENSIVE GZIP COMPRESSION DETECTION (SYNCHRONIZED WITH AUTO-FIX ENGINE)
     *
     * This method uses the same logic as the auto-fix engine to prevent contradictions
     */
    private function is_compression_enabled_comprehensive() {
        // Get the auto-fix engine instance to use its comprehensive detection
        if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
            // Fallback to basic detection if engine not available
            return $this->is_gzip_compression_enabled();
        }

        $engine = new Redco_Diagnostic_AutoFix_Engine();
        return $engine->is_compression_enabled_comprehensive();
    }

    /**
     * LEGACY METHOD: Keep for backward compatibility
     */
    private function is_gzip_compression_enabled() {
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            return false;
        }

        $content = file_get_contents($htaccess_file);
        return strpos($content, 'mod_deflate') !== false || strpos($content, 'mod_gzip') !== false;
    }

    private function needs_wordpress_update() {
        if (!function_exists('get_core_updates')) {
            require_once ABSPATH . 'wp-admin/includes/update.php';
        }

        $updates = get_core_updates();
        return !empty($updates) && isset($updates[0]) && $updates[0]->response === 'upgrade';
    }

    private function has_security_headers() {
        // Check if security headers are already configured
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file)) {
            return false;
        }

        $content = file_get_contents($htaccess_file);
        return strpos($content, 'X-Frame-Options') !== false || strpos($content, 'X-Content-Type-Options') !== false;
    }

    private function has_missing_meta_descriptions() {
        // Simple check - in a real implementation, this would scan actual pages
        return true; // Always suggest this as it requires manual content creation
    }

    private function get_database_cleanup_opportunities() {
        global $wpdb;

        // Count spam comments
        $spam_comments = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->comments} WHERE comment_approved = 'spam'");

        // Count trashed posts
        $trashed_posts = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_status = 'trash'");

        // Count post revisions
        $revisions = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->posts} WHERE post_type = 'revision'");

        $total_count = $spam_comments + $trashed_posts + $revisions;

        if ($total_count > 0) {
            return array(
                'count' => $total_count,
                'spam_comments' => $spam_comments,
                'trashed_posts' => $trashed_posts,
                'revisions' => $revisions
            );
        }

        return array();
    }

    private function get_plugin_update_count() {
        if (!function_exists('get_plugin_updates')) {
            require_once ABSPATH . 'wp-admin/includes/update.php';
        }

        $updates = get_plugin_updates();
        return count($updates);
    }

    /**
     * NEW: Additional fix implementations
     */
    private function fix_optimize_autoload() {
        global $wpdb;

        $result = array('success' => false, 'message' => '', 'changes_made' => array());

        // Get large autoload options
        $large_options = $wpdb->get_results(
            "SELECT option_name, LENGTH(option_value) as size
             FROM {$wpdb->options}
             WHERE autoload = 'yes'
             AND LENGTH(option_value) > 100000
             ORDER BY size DESC
             LIMIT 10"
        );

        if (empty($large_options)) {
            $result['success'] = true;
            $result['message'] = __('No large autoload options found to optimize', 'redco-optimizer');
            return $result;
        }

        $optimized_count = 0;
        foreach ($large_options as $option) {
            // Skip critical WordPress options
            $critical_options = array('active_plugins', 'stylesheet', 'template', 'current_theme');
            if (in_array($option->option_name, $critical_options)) {
                continue;
            }

            // Set to not autoload
            $wpdb->update(
                $wpdb->options,
                array('autoload' => 'no'),
                array('option_name' => $option->option_name),
                array('%s'),
                array('%s')
            );

            $optimized_count++;
            $result['changes_made'][] = "Set {$option->option_name} to not autoload (saved " . redco_format_bytes($option->size) . ")";
        }

        if ($optimized_count > 0) {
            $result['success'] = true;
            $result['message'] = sprintf(__('Optimized %d autoload options', 'redco-optimizer'), $optimized_count);
        } else {
            $result['success'] = true;
            $result['message'] = __('No autoload options could be safely optimized', 'redco-optimizer');
        }

        return $result;
    }

    private function fix_add_security_headers() {
        $result = array('success' => false, 'message' => '', 'changes_made' => array());

        $htaccess_file = ABSPATH . '.htaccess';

        if (!is_writable($htaccess_file) && file_exists($htaccess_file)) {
            $result['message'] = __('.htaccess file is not writable', 'redco-optimizer');
            return $result;
        }

        $security_headers = "
# BEGIN Redco Optimizer Security Headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options \"SAMEORIGIN\"
    Header always set X-Content-Type-Options \"nosniff\"
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Referrer-Policy \"strict-origin-when-cross-origin\"
    Header always set Permissions-Policy \"geolocation=(), microphone=(), camera=()\"
</IfModule>
# END Redco Optimizer Security Headers

";

        // CRITICAL FIX: Use BOM-safe reading to prevent Internal Server Errors
        if (method_exists($this, 'read_htaccess_safe')) {
            // Use the BOM-safe method from the auto-fix engine
            $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();
            $read_result = $autofix_engine->read_htaccess_safe($htaccess_file);

            if (!$read_result['success'] && file_exists($htaccess_file)) {
                $result['message'] = __('Failed to read .htaccess file: ', 'redco-optimizer') . $read_result['error'];
                return $result;
            }

            $current_content = $read_result['success'] ? $read_result['content'] : '';

            // Log BOM detection if found
            if ($read_result['bom_detected']) {
                error_log("REDCO: BOM detected in .htaccess file (" . $read_result['bom_type'] . ") - will be removed during security header fix");
                $result['changes_made'][] = 'Removed ' . $read_result['bom_type'] . ' BOM from .htaccess file';
            }
        } else {
            // Fallback to regular file reading
            $current_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';
        }

        // Check if rules already exist
        if (strpos($current_content, 'BEGIN Redco Optimizer Security Headers') !== false) {
            $result['message'] = __('Security headers already exist', 'redco-optimizer');
            $result['success'] = true;
            return $result;
        }

        // Add rules to .htaccess
        $new_content = $security_headers . $current_content;

        // CRITICAL FIX: Use BOM-safe writing to prevent Internal Server Errors
        if (method_exists($this, 'write_htaccess_safe')) {
            // Use the BOM-safe method from the auto-fix engine
            $autofix_engine = new Redco_Diagnostic_AutoFix_Engine();
            $write_result = $autofix_engine->write_htaccess_safe($htaccess_file, $new_content, true);

            if ($write_result['success']) {
                $result['success'] = true;
                $result['message'] = __('Security headers added successfully', 'redco-optimizer');
                $result['changes_made'][] = 'Added security headers to .htaccess';
                if ($write_result['backup_file']) {
                    $result['changes_made'][] = 'Created backup: ' . $write_result['backup_file'];
                }
            } else {
                $result['message'] = __('Failed to write to .htaccess file: ', 'redco-optimizer') . $write_result['error'];
            }
        } else {
            // Fallback to regular file writing
            if (file_put_contents($htaccess_file, $new_content)) {
                $result['success'] = true;
                $result['message'] = __('Security headers added successfully', 'redco-optimizer');
                $result['changes_made'][] = 'Added security headers to .htaccess';
            } else {
                $result['message'] = __('Failed to write to .htaccess file', 'redco-optimizer');
            }
        }

        return $result;
    }

    private function fix_cleanup_database() {
        global $wpdb;

        $result = array('success' => false, 'message' => '', 'changes_made' => array());

        $cleaned_items = 0;

        // Clean spam comments
        $spam_deleted = $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam'");
        if ($spam_deleted > 0) {
            $cleaned_items += $spam_deleted;
            $result['changes_made'][] = "Deleted {$spam_deleted} spam comments";
        }

        // Clean trashed posts
        $trash_deleted = $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_status = 'trash'");
        if ($trash_deleted > 0) {
            $cleaned_items += $trash_deleted;
            $result['changes_made'][] = "Deleted {$trash_deleted} trashed posts";
        }

        // Clean old revisions (keep last 3)
        $revisions_deleted = $wpdb->query("
            DELETE FROM {$wpdb->posts}
            WHERE post_type = 'revision'
            AND ID NOT IN (
                SELECT * FROM (
                    SELECT ID FROM {$wpdb->posts} p1
                    WHERE p1.post_type = 'revision'
                    ORDER BY p1.post_date DESC
                    LIMIT 3
                ) AS temp
            )
        ");
        if ($revisions_deleted > 0) {
            $cleaned_items += $revisions_deleted;
            $result['changes_made'][] = "Deleted {$revisions_deleted} old post revisions";
        }

        if ($cleaned_items > 0) {
            $result['success'] = true;
            $result['message'] = sprintf(__('Cleaned %d database entries', 'redco-optimizer'), $cleaned_items);
        } else {
            $result['success'] = true;
            $result['message'] = __('Database is already clean', 'redco-optimizer');
        }

        return $result;
    }

    /**
     * NEW: Backup and logging methods
     */
    private function create_optimization_backup($opportunity_id) {
        // Create a simple backup record
        $backup_id = 'backup_' . $opportunity_id . '_' . time();

        // Store backup info in options table
        $backup_data = array(
            'id' => $backup_id,
            'opportunity_id' => $opportunity_id,
            'timestamp' => current_time('mysql'),
            'htaccess_backup' => file_exists(ABSPATH . '.htaccess') ? file_get_contents(ABSPATH . '.htaccess') : ''
        );

        update_option('redco_optimization_backup_' . $backup_id, $backup_data);

        return $backup_id;
    }

    private function log_optimization_fix($opportunity_id, $title, $changes_made, $backup_id) {
        $log_entry = array(
            'opportunity_id' => $opportunity_id,
            'title' => $title,
            'changes_made' => $changes_made,
            'backup_id' => $backup_id,
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id()
        );

        // Get existing logs
        $logs = get_option('redco_optimization_logs', array());

        // Add new log entry
        $logs[] = $log_entry;

        // Keep only last 50 entries
        if (count($logs) > 50) {
            $logs = array_slice($logs, -50);
        }

        update_option('redco_optimization_logs', $logs);
    }

    /**
     * AJAX handler for getting real-time metrics (PERFORMANCE OPTIMIZED)
     */
    public function ajax_get_real_metrics() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_die('Security check failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }

        try {
            // Get full real-time metrics
            $metrics = $this->get_real_time_metrics_full();

            wp_send_json_success($metrics);
        } catch (Exception $e) {
            wp_send_json_error('Error getting metrics: ' . $e->getMessage());
        }
    }

    /**
     * CRITICAL FIX: AJAX handler for emergency recovery reports
     */
    public function ajax_emergency_recovery_report() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_diagnostic_nonce')) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'error_code' => 'NONCE_FAILED'
            ));
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'error_code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return;
        }

        try {
            $operation = sanitize_text_field($_POST['operation'] ?? 'unknown');
            $timestamp = intval($_POST['timestamp'] ?? time());
            $user_agent = sanitize_text_field($_POST['user_agent'] ?? '');

            // Log emergency recovery event
            $log_entry = array(
                'timestamp' => $timestamp,
                'operation' => $operation,
                'user_agent' => $user_agent,
                'user_id' => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'url' => $_SERVER['HTTP_REFERER'] ?? 'unknown'
            );

            // Store in emergency recovery log
            $recovery_logs = get_option('redco_emergency_recovery_logs', array());
            $recovery_logs[] = $log_entry;

            // Keep only last 50 entries
            if (count($recovery_logs) > 50) {
                $recovery_logs = array_slice($recovery_logs, -50);
            }

            update_option('redco_emergency_recovery_logs', $recovery_logs);

            // Log to WordPress debug log if enabled
            if (defined('WP_DEBUG') && WP_DEBUG && defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
                error_log('Redco Emergency Recovery: ' . $operation . ' operation timed out for user ' . get_current_user_id());
            }

            wp_send_json_success(array(
                'message' => 'Emergency recovery report logged',
                'logged_at' => current_time('mysql')
            ));

        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to log emergency recovery: ' . $e->getMessage(),
                'error_code' => 'LOGGING_FAILED'
            ));
        }
    }
}

// Auto-enable the module if not already enabled
add_action('admin_init', function() {
    $options = get_option('redco_optimizer_options', array());
    if (!isset($options['modules_enabled'])) {
        $options['modules_enabled'] = array();
    }
    if (!in_array('diagnostic-autofix', $options['modules_enabled'])) {
        $options['modules_enabled'][] = 'diagnostic-autofix';
        update_option('redco_optimizer_options', $options);
    }
});

// Initialize the module only if enabled and after init hook
function redco_init_diagnostic_autofix() {
    if (redco_is_module_enabled('diagnostic-autofix')) {
        new Redco_Diagnostic_AutoFix();
    }
}
add_action('init', 'redco_init_diagnostic_autofix', 10);
