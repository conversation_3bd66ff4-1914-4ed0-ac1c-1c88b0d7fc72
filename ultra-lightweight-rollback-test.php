<?php
/**
 * Ultra-Lightweight Rollback Test
 * Minimal memory footprint to avoid PHP memory exhaustion
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>⚡ Ultra-Lightweight Rollback Test</h1>\n";

$specific_backup_id = 'backup_2025-06-06_07-41-34_68429bae30c91';

// Minimal memory usage - get only count
$fix_history_count = count(get_option('redco_diagnostic_fix_history', array()));

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>📊 Current State</h3>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$fix_history_count}</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Execute ultra-lightweight rollback
if (isset($_GET['execute'])) {
    echo "<h2>⚡ Executing Ultra-Lightweight Rollback</h2>\n";
    
    // Create minimal test session if needed
    $fix_history = get_option('redco_diagnostic_fix_history', array());
    $backup_exists = false;
    
    foreach ($fix_history as $session) {
        if (($session['rollback_id'] ?? '') === $specific_backup_id || 
            ($session['backup_id'] ?? '') === $specific_backup_id) {
            $backup_exists = true;
            break;
        }
    }
    
    if (!$backup_exists) {
        // Add minimal session
        $fix_history[] = array(
            'session_id' => 'ultra_light_' . time(),
            'timestamp' => time(),
            'rollback_id' => $specific_backup_id,
            'backup_id' => $specific_backup_id,
            'message' => 'Ultra-light test',
            'fixes_applied' => 1,
            'backup_created' => true
        );
        update_option('redco_diagnostic_fix_history', $fix_history);
        echo "<p>✅ Created minimal test session</p>\n";
    }
    
    $initial_count = count($fix_history);
    
    try {
        // Load only the essential class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Minimal POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Rollback</h3>\n";
        echo "<p>Sessions before: {$initial_count}</p>\n";
        echo "</div>\n";
        
        // Execute with minimal output capture
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        // Check if it's valid JSON
        $response = json_decode($output, true);
        $is_json = ($response !== null);
        
        echo "<div style='background: " . ($is_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_json ? '✅' : '❌') . " AJAX Response</h3>\n";
        
        if ($is_json) {
            $success = $response['success'] ?? false;
            echo "<p><strong>Status:</strong> " . ($success ? 'SUCCESS' : 'FAILED') . "</p>\n";
            if ($success) {
                echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
            } else {
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
            }
        } else {
            echo "<p><strong>Invalid JSON Response</strong></p>\n";
            if (strlen($output) < 200) {
                echo "<p><code>" . htmlspecialchars($output) . "</code></p>\n";
            }
        }
        echo "</div>\n";
        
        // Check database result
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_history);
        $removed = $initial_count - $final_count;
        
        echo "<div style='background: " . ($removed > 0 ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($removed > 0 ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($removed > 0 ? '✅' : '❌') . " Database Result</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$initial_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$removed}</li>\n";
        echo "</ul>\n";
        
        if ($removed > 0) {
            echo "<p><strong>SUCCESS:</strong> The rollback successfully removed {$removed} session(s) from Recent Fixes!</p>\n";
        } else {
            echo "<p><strong>ISSUE:</strong> No sessions were removed from the database.</p>\n";
        }
        echo "</div>\n";
        
        // Check if specific backup ID still exists
        $still_exists = false;
        foreach ($final_history as $session) {
            if (($session['rollback_id'] ?? '') === $specific_backup_id || 
                ($session['backup_id'] ?? '') === $specific_backup_id) {
                $still_exists = true;
                break;
            }
        }
        
        echo "<div style='background: " . ($still_exists ? '#f8d7da' : '#d4edda') . "; border: 1px solid " . ($still_exists ? '#f5c6cb' : '#c3e6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($still_exists ? '❌' : '✅') . " Backup ID Check</h3>\n";
        if ($still_exists) {
            echo "<p>The backup ID still exists in the fix history.</p>\n";
        } else {
            echo "<p>The backup ID was successfully removed from the fix history.</p>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception</h3>\n";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Check the Diagnostic Module</a> to see if the Recent Fixes list was updated</li>\n";
    echo "<li>Refresh the diagnostic page to ensure changes persist</li>\n";
    echo "<li>If successful, the rolled-back item should no longer appear in Recent Fixes</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>⚡ Ready for Ultra-Lightweight Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 About This Test</h3>\n";
    echo "<p>This ultra-lightweight test is designed to avoid all memory issues by:</p>\n";
    echo "<ul>\n";
    echo "<li>Using minimal data structures</li>\n";
    echo "<li>Skipping heavy cache operations</li>\n";
    echo "<li>Avoiding large JSON logging</li>\n";
    echo "<li>Using efficient database operations</li>\n";
    echo "</ul>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>⚡ Execute Ultra-Lightweight Rollback</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
