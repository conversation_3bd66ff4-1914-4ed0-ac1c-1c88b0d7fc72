<?php
/**
 * Admin Test Runner for Diagnostic & Auto-Fix Module
 * 
 * Provides a safe way to run test scripts from WordPress admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied. This functionality is only available to administrators.');
}

// Add admin menu hook
add_action('admin_menu', 'redco_diagnostic_test_menu');

function redco_diagnostic_test_menu() {
    add_submenu_page(
        null, // No parent menu (hidden)
        'Diagnostic Tests',
        'Diagnostic Tests',
        'manage_options',
        'redco-diagnostic-tests',
        'redco_diagnostic_test_page'
    );
}

function redco_diagnostic_test_page() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_die('Access denied.');
    }
    
    $test_to_run = isset($_GET['test']) ? sanitize_text_field($_GET['test']) : '';
    
    ?>
    <div class="wrap">
        <h1>🔧 Diagnostic & Auto-Fix Module Tests</h1>
        
        <?php if (empty($test_to_run)): ?>
            <div class="notice notice-info">
                <p><strong>Available Test Scripts:</strong></p>
                <p>These tests help verify that the critical fixes for the Diagnostic & Auto-Fix module are working correctly.</p>
            </div>
            
            <div class="card" style="max-width: 800px;">
                <h2>Test Options</h2>
                
                <h3>🧪 Test 1: Comprehensive Diagnostic Fixes</h3>
                <p>Tests both critical issues:</p>
                <ul>
                    <li>Issue #1: Fixed issues reappearing on page refresh</li>
                    <li>Issue #2: Missing rollback buttons in Recent Fixes</li>
                </ul>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=comprehensive'); ?>" 
                       class="button button-primary">Run Comprehensive Test</a>
                </p>
                
                <h3>🔧 Test 2: Rollback Button Validation</h3>
                <p>Deep testing of rollback functionality:</p>
                <ul>
                    <li>Backup directory detection</li>
                    <li>Backup creation and validation</li>
                    <li>Migration function testing</li>
                    <li>AJAX response analysis</li>
                </ul>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=rollback'); ?>" 
                       class="button button-primary">Run Rollback Test</a>
                </p>
                
                <h3>📋 Instructions</h3>
                <ul>
                    <li>Tests are safe to run and won't affect your live site</li>
                    <li>Check the WordPress error log for detailed debugging information</li>
                    <li>Tests create temporary issues that are automatically cleaned up</li>
                    <li>Results will be displayed on this page</li>
                </ul>
            </div>
            
        <?php else: ?>
            <div class="notice notice-warning">
                <p><strong>Running Test:</strong> <?php echo esc_html($test_to_run); ?></p>
                <p>Please wait while the test completes...</p>
            </div>
            
            <div style="background: #f1f1f1; padding: 20px; border: 1px solid #ccc; font-family: monospace; white-space: pre-wrap;">
            <?php
            // Run the appropriate test
            if ($test_to_run === 'comprehensive') {
                include dirname(__FILE__) . '/test-comprehensive-diagnostic-fixes.php';
            } elseif ($test_to_run === 'rollback') {
                include dirname(__FILE__) . '/test-rollback-fix-validation.php';
            } else {
                echo "❌ Invalid test specified: " . esc_html($test_to_run);
            }
            ?>
            </div>
            
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests'); ?>" 
                   class="button">← Back to Test Menu</a>
            </p>
            
        <?php endif; ?>
    </div>
    <?php
}

// Initialize the admin menu if we're in admin
if (is_admin()) {
    redco_diagnostic_test_menu();
}
?>
