<?php
/**
 * Admin Test Runner for Diagnostic & Auto-Fix Module
 * 
 * Provides a safe way to run test scripts from WordPress admin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied. This functionality is only available to administrators.');
}

$test_to_run = isset($_GET['test']) ? sanitize_text_field($_GET['test']) : '';

?>
<div class="wrap">
    <h1>🔧 Diagnostic & Auto-Fix Module Tests</h1>
    
    <?php if (empty($test_to_run)): ?>
        <div class="notice notice-info">
            <p><strong>Available Test Scripts:</strong></p>
            <p>These tests help verify that the critical fixes for the Diagnostic & Auto-Fix module are working correctly.</p>
        </div>
        
        <div class="card" style="max-width: 800px;">
            <h2>Test Options</h2>
            
            <h3>🧪 Test 1: Comprehensive Diagnostic Fixes</h3>
            <p>Tests both critical issues:</p>
            <ul>
                <li>Issue #1: Fixed issues reappearing on page refresh</li>
                <li>Issue #2: Missing rollback buttons in Recent Fixes</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=comprehensive'); ?>" 
                   class="button button-primary">Run Comprehensive Test</a>
            </p>
            
            <h3>🔧 Test 2: Rollback Button Validation</h3>
            <p>Deep testing of rollback functionality:</p>
            <ul>
                <li>Backup directory detection</li>
                <li>Backup creation and validation</li>
                <li>Migration function testing</li>
                <li>AJAX response analysis</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=rollback'); ?>" 
                   class="button button-primary">Run Rollback Test</a>
            </p>
            
            <h3>🐘 Test 3: PHP 8.0+ Compatibility</h3>
            <p>Tests for PHP deprecation warnings:</p>
            <ul>
                <li>Null value handling in string functions</li>
                <li>Safe wrapper function testing</li>
                <li>Extract keywords function validation</li>
                <li>Dynamic guidance generation testing</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=php-compatibility'); ?>" 
                   class="button button-primary">Run PHP Compatibility Test</a>
            </p>
            
            <h3>🔍 Diagnostic 4: Rollback Unavailable Analysis</h3>
            <p>Analyzes specific items showing "(Rollback unavailable)":</p>
            <ul>
                <li>Examines fix history data structure</li>
                <li>Checks all backup storage locations</li>
                <li>Tests backup validation logic</li>
                <li>Identifies root causes of unavailable rollbacks</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=diagnose-unavailable'); ?>" 
                   class="button button-secondary">Diagnose Rollback Issues</a>
            </p>
            
            <h3>🔧 Fix 5: Rollback Unavailable Remediation</h3>
            <p>Fixes or removes items without valid rollback capability:</p>
            <ul>
                <li>Attempts to fix missing rollback IDs</li>
                <li>Validates backup existence</li>
                <li>Removes sessions without valid backups</li>
                <li>Cleans up Recent Fixes list</li>
            </ul>
            <p>
                <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests&test=fix-unavailable'); ?>" 
                   class="button button-primary">Fix Rollback Issues</a>
            </p>
            
            <h3>📋 Instructions</h3>
            <ul>
                <li>Tests are safe to run and won't affect your live site</li>
                <li>Check the WordPress error log for detailed debugging information</li>
                <li>Tests create temporary issues that are automatically cleaned up</li>
                <li>Results will be displayed on this page</li>
                <li><strong>Run "Diagnose Rollback Issues" first to identify problems</strong></li>
                <li><strong>Then run "Fix Rollback Issues" to resolve them</strong></li>
            </ul>
        </div>
        
    <?php else: ?>
        <div class="notice notice-warning">
            <p><strong>Running Test:</strong> <?php echo esc_html($test_to_run); ?></p>
            <p>Please wait while the test completes...</p>
        </div>
        
        <div style="background: #f1f1f1; padding: 20px; border: 1px solid #ccc; font-family: monospace; white-space: pre-wrap;">
        <?php
        // Run the appropriate test
        if ($test_to_run === 'comprehensive') {
            include dirname(__FILE__) . '/test-comprehensive-diagnostic-fixes.php';
        } elseif ($test_to_run === 'rollback') {
            include dirname(__FILE__) . '/test-rollback-fix-validation.php';
        } elseif ($test_to_run === 'php-compatibility') {
            include dirname(__FILE__) . '/test-php-compatibility.php';
        } elseif ($test_to_run === 'diagnose-unavailable') {
            include dirname(__FILE__) . '/diagnose-rollback-unavailable.php';
        } elseif ($test_to_run === 'fix-unavailable') {
            include dirname(__FILE__) . '/fix-rollback-unavailable.php';
        } else {
            echo "❌ Invalid test specified: " . esc_html($test_to_run);
        }
        ?>
        </div>
        
        <p>
            <a href="<?php echo admin_url('admin.php?page=redco-diagnostic-tests'); ?>" 
               class="button">← Back to Test Menu</a>
        </p>
        
    <?php endif; ?>
</div>
