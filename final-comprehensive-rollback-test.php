<?php
/**
 * Final Comprehensive Rollback Test
 * Tests the complete fixes for all identified issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🎯 Final Comprehensive Rollback Test</h1>\n";
echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🎉 Complete Solution Test</h3>\n";
echo "<p>This test verifies that ALL identified issues are now resolved:</p>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Recent Fixes List Updated</strong> - Database properly updated</li>\n";
echo "<li>✅ <strong>Backup Folder Created & Removed</strong> - Minimal structure created then cleaned</li>\n";
echo "<li>✅ <strong>.htaccess Files Created & Removed</strong> - Correct engine naming pattern</li>\n";
echo "<li>✅ <strong>Stats Updated</strong> - Enhanced logging and verification</li>\n";
echo "<li>✅ <strong>Memory Efficient</strong> - Under 10MB usage</li>\n";
echo "</ul>\n";
echo "</div>\n";

$specific_backup_id = 'backup_2025-06-06_08-52-41_6842ac593eed8';

// Get current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$current_stats = get_option('redco_diagnostic_stats', array());
$session_count = count($fix_history);

echo "<h2>📊 Pre-Test State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> {$session_count}</li>\n";
echo "<li><strong>Current Fixes Applied (Stats):</strong> " . ($current_stats['fixes_applied'] ?? 0) . "</li>\n";
echo "<li><strong>Target Backup ID:</strong> <code>{$specific_backup_id}</code></li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check current state of targets
$backup_dir = WP_CONTENT_DIR . '/uploads/redco-backups/' . $specific_backup_id;
$pre_backup_dir_exists = is_dir($backup_dir);
$pre_htaccess_files = glob(ABSPATH . '.htaccess.redco-backup-*');
$pre_htaccess_count = count($pre_htaccess_files);

echo "<h2>🔍 Pre-Test File Analysis</h2>\n";
echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
echo "<ul>\n";
echo "<li><strong>Backup Directory Exists:</strong> " . ($pre_backup_dir_exists ? '✅ YES' : '❌ NO') . "</li>\n";
echo "<li><strong>Existing .htaccess Backups:</strong> {$pre_htaccess_count} files</li>\n";
if ($pre_htaccess_files) {
    echo "<li><strong>Existing Files:</strong><ul>\n";
    foreach ($pre_htaccess_files as $file) {
        echo "<li>" . basename($file) . "</li>\n";
    }
    echo "</ul></li>\n";
}
echo "</ul>\n";
echo "</div>\n";

// Create test session if needed
$backup_found = false;
foreach ($fix_history as $session) {
    if (($session['rollback_id'] ?? '') === $specific_backup_id || 
        ($session['backup_id'] ?? '') === $specific_backup_id) {
        $backup_found = true;
        break;
    }
}

if (!$backup_found) {
    echo "<h2>🔧 Creating Test Session</h2>\n";
    
    $test_session = array(
        'session_id' => 'final_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Final comprehensive test session',
        'fixes_applied' => 1,
        'backup_created' => true
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    // Update stats to reflect the added fix
    $current_stats['fixes_applied'] = ($current_stats['fixes_applied'] ?? 0) + 1;
    update_option('redco_diagnostic_stats', $current_stats);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>✅ Created test session and updated stats for comprehensive testing</p>\n";
    echo "</div>\n";
    
    $session_count = count($fix_history);
}

// Execute final comprehensive test
if (isset($_GET['execute'])) {
    echo "<h2>🎯 Executing Final Comprehensive Test</h2>\n";
    
    $initial_memory = memory_get_usage();
    $pre_stats = get_option('redco_diagnostic_stats', array());
    $pre_fixes_count = $pre_stats['fixes_applied'] ?? 0;
    
    try {
        // Load diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Executing Complete Rollback Solution</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Sessions Before:</strong> {$session_count}</li>\n";
        echo "<li><strong>Stats Fixes Before:</strong> {$pre_fixes_count}</li>\n";
        echo "<li><strong>Backup Dir Before:</strong> " . ($pre_backup_dir_exists ? 'EXISTS' : 'NOT EXISTS') . "</li>\n";
        echo "<li><strong>.htaccess Files Before:</strong> {$pre_htaccess_count}</li>\n";
        echo "<li><strong>Memory Before:</strong> " . round($initial_memory / 1024 / 1024, 2) . " MB</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Execute rollback
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        $final_memory = memory_get_usage();
        $memory_used = $final_memory - $initial_memory;
        
        // Parse response
        $response = json_decode($output, true);
        $is_valid_json = ($response !== null);
        
        echo "<div style='background: " . ($is_valid_json ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($is_valid_json ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>" . ($is_valid_json ? '✅' : '❌') . " AJAX Response</h3>\n";
        
        if ($is_valid_json && ($response['success'] ?? false)) {
            echo "<p><strong>✅ SUCCESS:</strong> " . htmlspecialchars($response['data']['message'] ?? 'Success') . "</p>\n";
            
            if (isset($response['data']['cleanup_performed']['details'])) {
                $cleanup = $response['data']['cleanup_performed']['details'];
                echo "<h4>🧹 Cleanup Results:</h4>\n";
                echo "<ul>\n";
                if (!empty($cleanup['completed'])) {
                    foreach ($cleanup['completed'] as $task) {
                        echo "<li>✅ {$task}</li>\n";
                    }
                }
                if (!empty($cleanup['failed'])) {
                    foreach ($cleanup['failed'] as $task) {
                        echo "<li>❌ {$task}</li>\n";
                    }
                }
                echo "</ul>\n";
            }
        } else {
            echo "<p><strong>❌ FAILED:</strong> " . htmlspecialchars($response['data'] ?? $output) . "</p>\n";
        }
        echo "</div>\n";
        
        // Comprehensive post-test verification
        echo "<h3>🔍 Complete Post-Test Verification</h3>\n";
        
        // 1. Check Recent Fixes update
        $final_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_history);
        $sessions_removed = $session_count - $final_count;
        
        // 2. Check stats update
        $post_stats = get_option('redco_diagnostic_stats', array());
        $post_fixes_count = $post_stats['fixes_applied'] ?? 0;
        $stats_decremented = ($pre_fixes_count - $post_fixes_count);
        
        // 3. Check backup directory (should be created then removed)
        $post_backup_dir_exists = is_dir($backup_dir);
        
        // 4. Check .htaccess files (should be created then removed)
        $post_htaccess_files = glob(ABSPATH . '.htaccess.redco-backup-*');
        $post_htaccess_count = count($post_htaccess_files);
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📊 Complete Verification Matrix</h4>\n";
        echo "<table style='width: 100%; border-collapse: collapse;'>\n";
        echo "<tr><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Component</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Expected Behavior</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Actual Result</th><th style='text-align: left; padding: 8px; border-bottom: 1px solid #ddd;'>Status</th></tr>\n";
        
        // Recent Fixes
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Recent Fixes</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>Session removed</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$sessions_removed} removed</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($sessions_removed > 0 ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
        
        // Stats
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Stats Update</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>Fixes count decremented</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$stats_decremented} decremented</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($stats_decremented > 0 ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
        
        // Backup Directory
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>Backup Directory</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>Created then removed</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_backup_dir_exists ? 'Still exists' : 'Removed') . "</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . (!$post_backup_dir_exists ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
        
        // .htaccess Files
        echo "<tr><td style='padding: 8px; border-bottom: 1px solid #eee;'>.htaccess Backups</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>Created then removed</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>{$post_htaccess_count} remaining</td><td style='padding: 8px; border-bottom: 1px solid #eee;'>" . ($post_htaccess_count == 0 ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
        
        // Memory
        echo "<tr><td style='padding: 8px;'>Memory Usage</td><td style='padding: 8px;'>Under 10MB</td><td style='padding: 8px;'>" . round($memory_used / 1024 / 1024, 2) . " MB</td><td style='padding: 8px;'>" . ($memory_used < 10 * 1024 * 1024 ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
        
        echo "</table>\n";
        echo "</div>\n";
        
        // Final success analysis
        $recent_fixes_ok = $sessions_removed > 0;
        $stats_ok = $stats_decremented > 0;
        $backup_dir_ok = !$post_backup_dir_exists;
        $htaccess_ok = ($post_htaccess_count == 0);
        $memory_ok = $memory_used < 10 * 1024 * 1024;
        
        $all_tests_pass = $recent_fixes_ok && $stats_ok && $backup_dir_ok && $htaccess_ok && $memory_ok;
        
        echo "<div style='background: " . ($all_tests_pass ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($all_tests_pass ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>" . ($all_tests_pass ? '🎉' : '❌') . " FINAL TEST RESULT</h3>\n";
        
        if ($all_tests_pass) {
            echo "<p><strong>🎉 COMPLETE SUCCESS - ALL ISSUES RESOLVED!</strong></p>\n";
            echo "<p>The rollback functionality is now working perfectly with all identified problems fixed:</p>\n";
            echo "<ul>\n";
            echo "<li>✅ Recent Fixes list properly updated</li>\n";
            echo "<li>✅ Backup folder created and then cleaned up</li>\n";
            echo "<li>✅ .htaccess backup files created and then cleaned up</li>\n";
            echo "<li>✅ Stats updated and decremented correctly</li>\n";
            echo "<li>✅ Memory usage under control</li>\n";
            echo "</ul>\n";
            echo "<p><strong>🚀 The rollback system is production-ready!</strong></p>\n";
        } else {
            echo "<p><strong>❌ Some Tests Still Failing</strong></p>\n";
            echo "<p>Issues that need attention:</p>\n";
            echo "<ul>\n";
            if (!$recent_fixes_ok) echo "<li>❌ Recent Fixes list not updated</li>\n";
            if (!$stats_ok) echo "<li>❌ Stats not updated correctly</li>\n";
            if (!$backup_dir_ok) echo "<li>❌ Backup directory not cleaned up</li>\n";
            if (!$htaccess_ok) echo "<li>❌ .htaccess backup files not cleaned up</li>\n";
            if (!$memory_ok) echo "<li>❌ Memory usage too high</li>\n";
            echo "</ul>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Verify that the Recent Fixes list reflects the changes</li>\n";
    echo "<li>Check the error logs for detailed operation logs</li>\n";
    echo "<li>Test the rollback functionality with real fixes</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Check Diagnostic Module</a></p>\n";
    
} else {
    echo "<h2>🎯 Ready for Final Comprehensive Test</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 What This Final Test Will Verify</h3>\n";
    echo "<p>This test will verify the complete solution addresses ALL identified problems:</p>\n";
    echo "<ol>\n";
    echo "<li>✅ <strong>Backup folder is created</strong> (minimal structure) then removed</li>\n";
    echo "<li>✅ <strong>.htaccess files use correct naming</strong> (engine pattern) then removed</li>\n";
    echo "<li>✅ <strong>Recent Fixes list updated</strong> (session removed from database)</li>\n";
    echo "<li>✅ <strong>Stats updated correctly</strong> (fixes count decremented with verification)</li>\n";
    echo "<li>✅ <strong>Memory efficient operation</strong> (under 10MB usage)</li>\n";
    echo "</ol>\n";
    echo "<p><strong>This is the definitive test to confirm the rollback system is production-ready!</strong></p>\n";
    echo "<p><a href='?execute=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🎯 Execute Final Comprehensive Test</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='comprehensive-cleanup-verification-test.php'>🧪 Previous Test</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
